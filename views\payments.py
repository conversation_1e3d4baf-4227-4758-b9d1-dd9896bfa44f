"""
وحدة واجهة المدفوعات - توفر واجهة لإدارة مدفوعات العملاء
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
                            QFrame, QTableWidget, QTableWidgetItem, QHeaderView,
                            QSpinBox, QDoubleSpinBox, QDateEdit, QComboBox, QMessageBox,
                            QFormLayout, QLineEdit, QTabWidget, QWidget, QAbstractItemView,
                            QInputDialog)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QFont, QIcon
import datetime
from styles import AppStyles
try:
    from models.payments import PaymentModel
    from models.customers import CustomerModel
except ImportError:
    # إذا لم يتم العثور على المودل، نقوم بإنشاء كلاس بديل
    class PaymentModel:
        @staticmethod
        def get_customer_payments(customer_id):
            return []

        @staticmethod
        def get_invoice_payments(invoice_id):
            return []

        @staticmethod
        def get_customer_unpaid_invoices(customer_id):
            return []

        @staticmethod
        def add_payment(*args, **kwargs):
            return False, "PaymentModel غير متوفر"

        @staticmethod
        def delete_payment(*args, **kwargs):
            return False, "PaymentModel غير متوفر"

    class CustomerModel:
        @staticmethod
        def get_customer_total_debt(customer_id):
            return 0

class PaymentHistoryDialog(QDialog):
    """نافذة سجل المدفوعات للعميل"""

    def __init__(self, parent, customer_id, customer_name):
        super().__init__(parent)

        self.customer_id = customer_id
        self.customer_name = customer_name

        self.setWindowTitle(f"سجل المدفوعات - {customer_name}")
        self.setMinimumSize(800, 600)
        self.setLayoutDirection(Qt.RightToLeft)

        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout(self)

        # عنوان النافذة
        title_layout = QHBoxLayout()
        title_label = QLabel(f"سجل المدفوعات للعميل: {customer_name}")
        title_label.setObjectName("page_title")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_layout.addWidget(title_label)

        main_layout.addLayout(title_layout)

        # إضافة فاصل
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setObjectName("content_separator")
        main_layout.addWidget(separator)
        main_layout.addSpacing(10)

        # معلومات الدين
        info_layout = QHBoxLayout()

        self.total_debt_label = QLabel("إجمالي الدين المتبقي: 0.00 ج.م")
        self.total_debt_label.setObjectName("stats_label")
        self.total_debt_label.setStyleSheet("""
            background-color: #fee2e2;
            color: #dc2626;
            padding: 8px 15px;
            border-radius: 5px;
            font-weight: bold;
            border: 1px solid #fecaca;
            font-size: 16px;
        """)

        self.total_payments_label = QLabel("إجمالي المدفوعات: 0.00 ج.م")
        self.total_payments_label.setObjectName("stats_label")
        self.total_payments_label.setStyleSheet("""
            background-color: #eef2ff;
            color: #2563eb;
            padding: 8px 15px;
            border-radius: 5px;
            font-weight: bold;
            border: 1px solid #dbeafe;
            font-size: 16px;
        """)

        info_layout.addWidget(self.total_debt_label)
        info_layout.addStretch()
        info_layout.addWidget(self.total_payments_label)

        main_layout.addLayout(info_layout)
        main_layout.addSpacing(10)

        # زر دفع دين جديد
        payment_button_layout = QHBoxLayout()

        add_payment_button = QPushButton("💰 تسجيل دفعة جديدة")
        add_payment_button.setObjectName("action_button")
        add_payment_button.setMinimumHeight(50)
        add_payment_button.setMinimumWidth(200)
        add_payment_button.clicked.connect(self.add_new_payment)

        payment_button_layout.addStretch()
        payment_button_layout.addWidget(add_payment_button)
        payment_button_layout.addStretch()

        main_layout.addLayout(payment_button_layout)
        main_layout.addSpacing(20)

        # عنوان جدول المدفوعات
        table_title = QLabel("سجل المدفوعات السابقة")
        table_title.setObjectName("section_title")
        table_title.setFont(QFont("Arial", 14, QFont.Bold))
        main_layout.addWidget(table_title)

        # جدول المدفوعات
        self.payments_table = QTableWidget()
        self.payments_table.setColumnCount(4)
        self.payments_table.setHorizontalHeaderLabels(["التاريخ", "المبلغ", "طريقة الدفع", "ملاحظات"])
        self.payments_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.payments_table.verticalHeader().setVisible(False)
        self.payments_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.payments_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.payments_table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.payments_table.setAlternatingRowColors(True)

        # إعداد فعل النقر بزر الماوس الأيمن على جدول المدفوعات
        self.payments_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.payments_table.customContextMenuRequested.connect(self.show_payment_context_menu)

        main_layout.addWidget(self.payments_table)

        # أزرار أسفل النافذة
        buttons_layout = QHBoxLayout()

        self.close_button = QPushButton("إغلاق")
        self.close_button.clicked.connect(self.reject)

        refresh_button = QPushButton("تحديث البيانات")
        refresh_button.setObjectName("secondary_button")
        refresh_button.clicked.connect(self.refresh_data)

        buttons_layout.addWidget(refresh_button)
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.close_button)

        main_layout.addLayout(buttons_layout)

        # تحميل البيانات
        self.load_data()

        # تطبيق الأنماط
        self.apply_styles()

    def load_data(self):
        """تحميل البيانات من قاعدة البيانات"""
        self.load_payment_history()
        self.update_totals()

    def load_payment_history(self):
        """تحميل سجل المدفوعات"""
        # تفريغ الجدول
        self.payments_table.clearContents()
        self.payments_table.setRowCount(0)

        # استرجاع سجل المدفوعات
        payments = PaymentModel.get_customer_payments(self.customer_id)

        if not payments:
            row = self.payments_table.rowCount()
            self.payments_table.insertRow(row)

            empty_item = QTableWidgetItem("لا توجد مدفوعات سابقة")
            empty_item.setTextAlignment(Qt.AlignCenter)
            self.payments_table.setSpan(row, 0, 1, 4)
            self.payments_table.setItem(row, 0, empty_item)

            return

        # إضافة المدفوعات إلى الجدول
        for payment in payments:
            row = self.payments_table.rowCount()
            self.payments_table.insertRow(row)

            # تخزين معرف المدفوعة كبيانات مخفية في الصف
            self.payments_table.setProperty(f"payment_id_{row}", payment['id'])

            # تاريخ الدفع (العمود 0 الآن)
            date_item = QTableWidgetItem(payment['payment_date'])
            date_item.setTextAlignment(Qt.AlignCenter)
            self.payments_table.setItem(row, 0, date_item)

            # المبلغ (العمود 1 الآن)
            amount_item = QTableWidgetItem(f"{payment['amount']:.2f} ج.م")
            amount_item.setTextAlignment(Qt.AlignCenter)
            self.payments_table.setItem(row, 1, amount_item)

            # طريقة الدفع (العمود 2 الآن)
            method_item = QTableWidgetItem(payment['payment_method'])
            method_item.setTextAlignment(Qt.AlignCenter)
            self.payments_table.setItem(row, 2, method_item)

            # ملاحظات (العمود 3 الآن)
            notes_item = QTableWidgetItem(payment['notes'] if payment['notes'] else "-")
            notes_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.payments_table.setItem(row, 3, notes_item)

    def update_totals(self):
        """تحديث إجماليات المدفوعات والديون"""
        total_payments = 0

        # حساب إجمالي المدفوعات من جدول المدفوعات
        for row in range(self.payments_table.rowCount()):
            # تجنب صف "لا توجد مدفوعات سابقة" الذي لا يحتوي على بيانات رقمية
            if (self.payments_table.item(row, 1) and
                not (self.payments_table.item(row, 0) and
                     self.payments_table.item(row, 0).text() == "لا توجد مدفوعات سابقة")):
                amount_text = self.payments_table.item(row, 1).text().split()[0]
                try:
                    total_payments += float(amount_text)
                except (ValueError, IndexError):
                    continue

        # الحصول على إجمالي الدين المتبقي
        total_debt = CustomerModel.get_customer_total_debt(self.customer_id)

        # تحديث النصوص
        self.total_payments_label.setText(f"إجمالي المدفوعات: {total_payments:.2f} ج.م")
        self.total_debt_label.setText(f"إجمالي الدين المتبقي: {total_debt:.2f} ج.م")

    def add_new_payment(self):
        """إضافة دفعة جديدة"""
        # الحصول على إجمالي الدين المتبقي
        total_debt = CustomerModel.get_customer_total_debt(self.customer_id)

        if total_debt <= 0:
            QMessageBox.information(
                self,
                "لا يوجد دين",
                "لا يوجد دين مستحق على هذا العميل."
            )
            return

        # إنشاء نافذة حوار لإدخال المبلغ مع تطبيق التنسيقات
        from styles import AppStyles

        # إنشاء QInputDialog وتطبيق التنسيقات عليه
        input_dialog = QInputDialog(self)
        input_dialog.setWindowTitle("دفع دين")
        input_dialog.setLabelText(f"أدخل المبلغ المراد دفعه (الحد الأقصى: {total_debt:.2f} ج.م):")
        input_dialog.setInputMode(QInputDialog.DoubleInput)
        input_dialog.setDoubleValue(min(total_debt, 100))
        input_dialog.setDoubleRange(1, total_debt)
        input_dialog.setDoubleDecimals(2)

        # تطبيق التنسيقات على النافذة
        input_dialog.setStyleSheet(AppStyles.get_input_dialog_style())

        # عرض النافذة والحصول على النتيجة
        ok = input_dialog.exec_() == QInputDialog.Accepted
        amount = input_dialog.doubleValue() if ok else 0

        if not ok or amount <= 0:
            return

        # استخدام خاصية دفع الدين الإجمالي مباشرة بدون نافذة تأكيد
        try:
            # تجميع بيانات الدفعة مع قيم افتراضية
            payment_data = {
                'customer_id': self.customer_id,
                'amount': amount,
                'payment_date': QDate.currentDate().toString("yyyy/MM/dd"),
                'payment_method': "نقداً",  # طريقة دفع افتراضية
                'notes': "دفع دين",         # ملاحظة افتراضية
                'payment_mode': 'total_debt'  # تحديد أن الدفعة هي دفع إجمالي للدين
            }

            # استدعاء خدمة إضافة الدفعة
            # payment_result هو payment_id أو None
            payment_result = PaymentModel.add_payment(payment_data)

            if payment_result is not None: # التحقق إذا كانت العملية ناجحة (payment_id ليس None)
                QMessageBox.information(
                    self,
                    "تم بنجاح",
                    f"تم تسجيل الدفعة بنجاح بقيمة {amount:.2f} ج.م."
                )
                # إعادة تحميل البيانات
                self.refresh_data()
            else:
                # عرض رسالة خطأ عامة لأن PaymentModel.add_payment لا ترجع رسالة خطأ محددة
                QMessageBox.warning(
                    self,
                    "خطأ",
                    "فشل في تسجيل الدفعة. يرجى مراجعة سجلات الأخطاء."
                )

        except Exception as e:
            import traceback
            error_details = traceback.format_exc()
            print(f"خطأ في إضافة دفعة: {str(e)}\n{error_details}")

            QMessageBox.warning(
                self,
                "خطأ",
                f"فشل في تسجيل الدفعة: {str(e)}"
            )

    def show_payment_context_menu(self, position):
        """عرض قائمة السياق عند النقر بزر الماوس الأيمن على جدول المدفوعات"""
        # التحقق من وجود صف محدد
        indexes = self.payments_table.selectedIndexes()
        if not indexes or self.payments_table.item(indexes[0].row(), 0).text() == "لا توجد مدفوعات سابقة":
            return

        # الحصول على معرف الدفعة المحددة من خاصية الصف
        row = indexes[0].row()
        payment_id = self.payments_table.property(f"payment_id_{row}")

        if not payment_id:
            return

        # إنشاء القائمة
        menu = QMessageBox.question(
            self,
            "حذف الدفعة",
            "هل أنت متأكد من رغبتك في حذف هذه الدفعة؟\nسيتم إعادة المبلغ المدفوع إلى رصيد الدين.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if menu == QMessageBox.Yes:
            self.delete_payment(payment_id)

    def delete_payment(self, payment_id):
        """حذف دفعة من قاعدة البيانات"""
        success, message = PaymentModel.delete_payment(payment_id)

        if success:
            QMessageBox.information(
                self,
                "تم بنجاح",
                "تم حذف الدفعة بنجاح."
            )

            # إعادة تحميل البيانات
            self.refresh_data()
        else:
            QMessageBox.warning(
                self,
                "خطأ",
                f"فشل في حذف الدفعة: {message}"
            )

    def refresh_data(self):
        """إعادة تحميل البيانات"""
        self.load_data()

    def apply_styles(self):
        """تطبيق الأنماط على النافذة"""
        self.setStyleSheet(AppStyles.get_dialog_style())