import sys
import os
import datetime
import subprocess
import io
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                             QHBoxLayout, QPushButton, QLabel, QStackedWidget,
                             QFrame, QSizePolicy, QSpacerItem, QStatusBar, QDesktopWidget,
                             QMessageBox, QSplashScreen, QMenu)
from PyQt5.QtCore import Qt, QSize, QTimer, QtMsgType, qInstallMessageHandler
from PyQt5.QtGui import QIcon, QFont, QPixmap, QFontDatabase, QPainter, QColor

# إعداد نظام التسجيل لتوجيه الرسائل إلى ملف بدلاً من وحدة التحكم
DEBUG_MODE = False  # تعيين القيمة True للتطوير وFalse للإنتاج
LOG_FILE = "app.log"

# دالة للطباعة في وحدة التحكم حتى في وضع الإنتاج
def console_print(*args, **kwargs):
    """وظيفة للطباعة في وحدة التحكم حتى عندما يتم إعادة توجيه الإخراج العادي إلى ملف"""
    if DEBUG_MODE:
        print(*args, **kwargs)
    else:
        # طباعة في ملف السجل
        print(*args, **kwargs)
        # طباعة في وحدة التحكم أيضا
        print(*args, file=original_stdout, **kwargs)

# تهيئة ملف السجل
if not DEBUG_MODE:
    # إنشاء مجلد logs إذا لم يكن موجودًا
    os.makedirs("logs", exist_ok=True)
    log_path = os.path.join("logs", LOG_FILE)

    # حفظ مرجع لوحدة الطباعة الأصلية
    original_stdout = sys.stdout
    original_stderr = sys.stderr

    # فتح ملف السجل للكتابة
    log_file = open(log_path, 'a', encoding='utf-8')

    # إعادة توجيه الإخراج إلى ملف السجل - طريقة بسيطة ومباشرة
    sys.stdout = log_file
    sys.stderr = log_file

    # تفعيل الكتابة الفورية في ملف السجل
    sys.stdout.flush()
    sys.stderr.flush()

    # تفعيل الكتابة الفورية في ملف السجل
    sys.stdout.flush()

# إضافة معالج الرسائل المخصص لمنع ظهور رسائل التحذير في وحدة التحكم
def suppress_qt_warnings(msg_type, context, message):
    # تجاهل رسائل "Unknown property direction"
    if "Unknown property direction" in message:
        return

    # طباعة الرسائل المهمة فقط (الخطأ الشديد والمهلك)
    if msg_type in (QtMsgType.QtCriticalMsg, QtMsgType.QtFatalMsg):
        if not DEBUG_MODE:
            console_print(f"Qt Error: {message}")
        else:
            pass

# استيراد ملف التنسيقات
from styles import AppStyles

# استيراد مكونات قاعدة البيانات
from models.database import db
from controllers.database_controller import DatabaseController

# استيراد مكونات المستخدمين والمصادقة
from controllers.user_controller import UserController
from views.login import LoginWindow

# استيراد الصفحات
from views.sales import SalesView
from views.products import ProductsView
from views.invoices import InvoicesView
from views.reports import ReportsView
from views.customers import CustomersView
from views.suppliers import SuppliersView
from views.settings import SettingsView
from views.expenses import ExpensesView

class MainWindow(QMainWindow):
    def __init__(self, user_data=None):
        print("بدء إنشاء النافذة الرئيسية...")
        super().__init__()
        print("تم استدعاء منشئ الفئة الأب")

        # تخزين بيانات المستخدم الحالي
        self.current_user = user_data or {}
        print(f"تم تخزين بيانات المستخدم: {self.current_user}")

        # قائمة الصلاحيات المطلوبة لكل صفحة
        print("جاري تعيين الصلاحيات...")
        self.required_permissions = {
            0: "عرض المبيعات",      # المبيعات
            1: "عرض المنتجات",      # المنتجات
            2: "عرض الفواتير",      # الفواتير
            3: "عرض تقارير المبيعات",  # التقارير
            4: "عرض العملاء",       # العملاء
            5: "عرض الموردين",      # الموردين
            6: "عرض المصروفات",     # المصروفات
            7: "عرض الإعدادات"      # الإعدادات
        }

        # إعداد النافذة الرئيسية
        self.setWindowTitle("SMART MANAGER")
        self.setMinimumSize(800, 600)
        self.setLayoutDirection(Qt.RightToLeft)  # تعيين اتجاه التخطيط من اليمين إلى اليسار

        # ضبط حجم النافذة بناءً على حجم الشاشة (75% عرض و70% ارتفاع)
        self.adjust_window_size()

        # إعداد التخطيط الرئيسي
        main_widget = QWidget()
        main_layout = QHBoxLayout(main_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # إنشاء الشريط الجانبي للتنقل
        self.sidebar_widget = QWidget()
        self.sidebar_widget.setObjectName("sidebar")
        self.sidebar_widget.setFixedWidth(240)  # زيادة عرض الشريط الجانبي قليلاً
        self.sidebar_widget.setLayoutDirection(Qt.LeftToRight)  # الشريط الجانبي من اليسار إلى اليمين

        sidebar_layout = QVBoxLayout(self.sidebar_widget)
        sidebar_layout.setContentsMargins(0, 0, 0, 0)
        sidebar_layout.setSpacing(0)

        # عنوان التطبيق في الشريط الجانبي
        title_widget = QWidget()
        title_widget.setObjectName("title_widget")
        title_widget.setMinimumHeight(80)  # زيادة ارتفاع رأس الشريط الجانبي

        title_layout = QVBoxLayout(title_widget)
        title_label = QLabel("SMART MANAGER")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setFont(AppStyles.get_system_font(14, bold=True))
        title_label.setObjectName("title_label")
        title_layout.addWidget(title_label)

        sidebar_layout.addWidget(title_widget)

        # إضافة فاصل بعد العنوان
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setObjectName("sidebar_separator")
        sidebar_layout.addWidget(separator)
        sidebar_layout.addSpacing(10)  # إضافة مسافة بعد الفاصل

        # إنشاء أزرار التنقل
        self.nav_buttons = []
        nav_items = [
            {"name": "المبيعات", "view": SalesView, "icon": "🛒"},
            {"name": "المنتجات", "view": ProductsView, "icon": "📦"},
            {"name": "الفواتير", "view": InvoicesView, "icon": "📄"},
            {"name": "التقارير", "view": ReportsView, "icon": "📊"},
            {"name": "العملاء", "view": CustomersView, "icon": "👥"},
            {"name": "الموردين", "view": SuppliersView, "icon": "🏭"},
            {"name": "المصروفات", "view": ExpensesView, "icon": "💰"},
            {"name": "الإعدادات", "view": SettingsView, "icon": "⚙️"}
        ]

        # إنشاء StackedWidget للصفحات
        print("جاري إنشاء StackedWidget...")
        self.content_widget = QStackedWidget()
        print("تم إنشاء StackedWidget")

        # إنشاء إطار لتجميع أزرار التنقل
        nav_frame = QFrame()
        nav_frame.setObjectName("nav_frame")
        nav_layout = QVBoxLayout(nav_frame)
        nav_layout.setContentsMargins(15, 0, 15, 0)
        nav_layout.setSpacing(8)

        for i, item in enumerate(nav_items):
            print(f"جاري إنشاء زر وصفحة: {item['name']}")
            # إنشاء زر التنقل
            nav_button = QPushButton(f"{item['icon']}  {item['name']}")
            nav_button.setObjectName("nav_button")
            nav_button.setMinimumHeight(50)
            nav_button.setFont(AppStyles.get_system_font(11))
            nav_button.setCheckable(True)
            nav_button.clicked.connect(lambda checked, idx=i: self.change_page(idx))

            self.nav_buttons.append(nav_button)
            nav_layout.addWidget(nav_button)
            print(f"تم إنشاء زر: {item['name']}")

            # إضافة الصفحة إلى StackedWidget
            try:
                print(f"جاري إنشاء صفحة: {item['name']}")
                page = item["view"]()
                print(f"تم إنشاء صفحة: {item['name']}")
                page.setLayoutDirection(Qt.RightToLeft)  # تعيين اتجاه التخطيط لكل صفحة
                self.content_widget.addWidget(page)
                print(f"تم إضافة صفحة: {item['name']} إلى StackedWidget")
            except Exception as e:
                print(f"خطأ في إنشاء صفحة {item['name']}: {e}")
                import traceback
                traceback.print_exc()
                # إضافة صفحة فارغة بدلاً من التوقف
                placeholder = QWidget()
                placeholder_layout = QVBoxLayout(placeholder)
                error_label = QLabel(f"خطأ في تحميل صفحة {item['name']}")
                error_label.setAlignment(Qt.AlignCenter)
                placeholder_layout.addWidget(error_label)
                self.content_widget.addWidget(placeholder)

        # إضافة زر الآلة الحاسبة
        calc_button = QPushButton("🧮  الآلة الحاسبة")
        calc_button.setObjectName("nav_button")
        calc_button.setMinimumHeight(50)
        calc_button.setFont(AppStyles.get_system_font(11))
        calc_button.clicked.connect(self.open_calculator)
        nav_layout.addWidget(calc_button)

        # إضافة إطار الأزرار إلى الشريط الجانبي
        sidebar_layout.addWidget(nav_frame)

        # إضافة مساحة فارغة في نهاية الشريط الجانبي
        sidebar_layout.addItem(QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding))

        # إضافة معلومات في أسفل الشريط الجانبي
        footer_label = QLabel("SMART MANAGER © 2025")
        footer_label.setObjectName("footer_label")
        footer_label.setAlignment(Qt.AlignCenter)
        sidebar_layout.addWidget(footer_label)
        sidebar_layout.addSpacing(10)

        # إضافة الشريط الجانبي والمحتوى إلى التخطيط الرئيسي
        main_layout.addWidget(self.sidebar_widget)

        # إضافة فاصل بين الشريط الجانبي والمحتوى
        # En lugar de usar un simple QFrame, crear un QWidget personalizado para el separador
        content_separator = QWidget()
        content_separator.setObjectName("content_separator")
        # Establecer un ancho fijo para el separador (4px es suficiente para el efecto de sombra)
        content_separator.setFixedWidth(4)

        # Aplicar un estilo personalizado al separador que incluya un gradiente más marcado
        content_separator.setStyleSheet("""
            #content_separator {
                background-color: qlineargradient(
                    x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(180, 180, 180, 0.9),
                    stop:0.4 rgba(210, 210, 210, 0.4),
                    stop:1 rgba(240, 240, 240, 0.1)
                );
                border: none;
                margin: 5px 0px;
            }
        """)

        main_layout.addWidget(content_separator)

        main_layout.addWidget(self.content_widget)

        # تحديد الصفحة الافتراضية
        self.change_page(0)

        # إضافة الـ Widget الرئيسي إلى النافذة
        self.setCentralWidget(main_widget)

        # إنشاء شريط الحالة السفلي
        self.statusBar = QStatusBar()
        self.statusBar.setObjectName("status_bar")
        self.setStatusBar(self.statusBar)

        # إنشاء عناصر شريط الحالة
        self.status_username = QLabel("المستخدم: غير معروف")
        self.status_username.setObjectName("status_username")
        self.status_username.setAlignment(Qt.AlignCenter)
        self.status_username.setMinimumWidth(200)

        # تفعيل قائمة السياق على اسم المستخدم
        self.status_username.setContextMenuPolicy(Qt.CustomContextMenu)
        self.status_username.customContextMenuRequested.connect(self.show_user_context_menu)

        # عنصر مطاط لدفع اسم المستخدم إلى أقصى اليمين
        spacer_label = QLabel()
        spacer_label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.status_datetime = QLabel()
        self.status_datetime.setObjectName("status_item")
        self.status_datetime.setAlignment(Qt.AlignCenter)

        # حالة الاتصال بقاعدة البيانات
        self.status_db = QLabel()
        self.status_db.setObjectName("status_item")
        self.status_db.setAlignment(Qt.AlignCenter)
        self.update_db_status()

        # فاصل
        separator = QLabel("  |  ")
        separator.setObjectName("status_separator")

        # إضافة عناصر إلى شريط الحالة (من اليسار إلى اليمين في العرض من اليمين إلى اليسار)
        self.statusBar.addWidget(self.status_username)  # سيظهر في أقصى اليمين
        self.statusBar.addWidget(separator)
        self.statusBar.addWidget(self.status_db)  # حالة الاتصال بقاعدة البيانات
        self.statusBar.addWidget(spacer_label)  # سيدفع اسم المستخدم لأقصى اليمين ويملأ المساحة الوسطى
        self.statusBar.addPermanentWidget(self.status_datetime)  # سيظهر في أقصى اليسار

        # إنشاء مؤقت لتحديث الوقت والتاريخ
        self.datetime_timer = QTimer(self)
        self.datetime_timer.timeout.connect(self.update_datetime)
        self.datetime_timer.start(1000)  # تحديث كل ثانية
        self.update_datetime()  # تحديث فوري

        # تطبيق الأنماط
        self.apply_styles()

        # تحديد اسم المستخدم في شريط الحالة
        print("جاري تحديث معلومات المستخدم...")
        self.update_user_info()
        print("تم إنشاء النافذة الرئيسية بنجاح")

    def adjust_window_size(self):
        """ضبط حجم النافذة ليكون 80% من عرض الشاشة و75% من ارتفاعها"""
        # الحصول على أبعاد الشاشة
        desktop = QDesktopWidget().availableGeometry()
        screen_width = desktop.width()
        screen_height = desktop.height()

        # حساب حجم النافذة المطلوب (80% عرض و76% ارتفاع)
        window_width = int(screen_width * 0.80)
        window_height = int(screen_height * 0.76)

        # تحديد موقع النافذة في المنتصف
        x_position = (screen_width - window_width) // 2
        y_position = (screen_height - window_height) // 2

        # تطبيق الحجم والموقع
        self.setGeometry(x_position, y_position, window_width, window_height)

    def resizeEvent(self, event):
        """معالجة أحداث تغيير حجم النافذة الرئيسية"""
        super().resizeEvent(event)

        # تحديث أزرار المنتجات المفضلة في صفحة المبيعات عند تغيير حجم النافذة
        try:
            # البحث عن صفحة المبيعات في التطبيق
            sales_page = None
            for i in range(self.content_widget.count()):
                widget = self.content_widget.widget(i)
                if hasattr(widget, 'resize_buttons_grid'):  # التحقق من وجود دالة تغيير حجم الأزرار
                    sales_page = widget
                    break

            # إذا تم العثور على صفحة المبيعات، قم بتحديث أزرار المنتجات المفضلة بكفاءة
            if sales_page and hasattr(sales_page, 'resize_timer'):
                # استخدام المؤقت لتجميع التغييرات وتوفير الموارد
                sales_page.resize_timer.start(300)  # تأخير أطول لتوفير الموارد

        except Exception as e:
            # تجاهل الأخطاء لتجنب تعطيل النافذة الرئيسية
            pass

    def change_page(self, index):
        """تغيير الصفحة الحالية وتحديث حالة أزرار التنقل"""
        # التحقق من صلاحيات المستخدم قبل الانتقال إلى الصفحة المطلوبة
        from controllers.user_controller import UserController

        # التحقق من صلاحية المستخدم للوصول إلى الصفحة المطلوبة
        if index in self.required_permissions and self.current_user and 'id' in self.current_user:
            user_id = self.current_user['id']
            permission = self.required_permissions[index]

            # المستخدم admin له جميع الصلاحيات
            if self.current_user.get('username') != 'admin':
                # التحقق من صلاحية المستخدم
                if not UserController.check_permission(user_id, permission, show_message=True, parent_widget=self):
                    # إذا لم يكن لدى المستخدم الصلاحية، لا يتم الانتقال إلى الصفحة
                    # تحديث حالة أزرار التنقل للعودة إلى الصفحة الحالية
                    current_index = self.content_widget.currentIndex()
                    for i, button in enumerate(self.nav_buttons):
                        button.setChecked(i == current_index)
                    return

        # الانتقال إلى الصفحة المطلوبة
        self.content_widget.setCurrentIndex(index)

        # تحديث حالة أزرار التنقل
        for i, button in enumerate(self.nav_buttons):
            button.setChecked(i == index)

        # تحديث الصفحة الحالية عند الانتقال إليها
        current_page = self.content_widget.currentWidget()

        # التحقق من وجود وظيفة تحديث في الصفحة وتنفيذها
        if hasattr(current_page, 'refresh_page') and callable(current_page.refresh_page):
            current_page.refresh_page()

        # تحديث حالة الاتصال بقاعدة البيانات في شريط الحالة
        self.update_db_status()

    def update_datetime(self):
        """تحديث عرض الوقت والتاريخ في شريط الحالة"""
        now = datetime.datetime.now()

        # تهيئة التاريخ بالتنسيق العربي
        date_str = now.strftime("%Y/%m/%d")

        # تهيئة الوقت بتنسيق AM/PM
        hour = now.hour
        minute = now.minute
        second = now.second
        period = "مساءً" if hour >= 12 else "صباحًا"

        # تحويل الساعة إلى نظام 12 ساعة
        if hour > 12:
            hour -= 12
        elif hour == 0:
            hour = 12

        # تنسيق الوقت بالشكل المناسب
        time_str = f"{hour:02d}:{minute:02d}:{second:02d} {period}"

        # عرض التاريخ والوقت في شريط الحالة
        self.status_datetime.setText(f"التاريخ: {date_str}  |  الوقت: {time_str}")

    def update_db_status(self):
        """تحديث حالة الاتصال بقاعدة البيانات في شريط الحالة"""
        success, _ = DatabaseController.test_connection()

        # Verificar si el atributo status_db existe antes de usarlo
        if hasattr(self, 'status_db'):
            if success:
                self.status_db.setText("حالة قاعدة البيانات: متصلة")
                self.status_db.setStyleSheet("color: green;")
            else:
                self.status_db.setText("حالة قاعدة البيانات: غير متصلة")
                self.status_db.setStyleSheet("color: red;")

    def open_calculator(self):
        """فتح الآلة الحاسبة الخاصة بنظام ويندوز"""
        try:
            subprocess.Popen("calc.exe")
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"لا يمكن فتح الآلة الحاسبة: {str(e)}")

    def show_user_context_menu(self, position):
        """عرض قائمة السياق لاسم المستخدم"""
        # التحقق من وجود مستخدم مسجل دخول
        if not self.current_user or 'username' not in self.current_user:
            return

        # إنشاء قائمة السياق
        context_menu = QMenu(self)
        context_menu.setLayoutDirection(Qt.RightToLeft)

        # إضافة معلومات المستخدم كعنوان
        user_fullname = self.current_user.get('full_name', self.current_user['username'])
        user_info_action = context_menu.addAction(f"👤  {user_fullname}")
        user_info_action.setEnabled(False)  # جعله غير قابل للنقر (عنوان فقط)

        context_menu.addSeparator()

        # إضافة زر تسجيل الخروج
        logout_action = context_menu.addAction("🚪  تسجيل خروج")
        logout_action.setToolTip("تسجيل الخروج من النظام")
        logout_action.triggered.connect(self.logout)

        # تطبيق الأنماط على قائمة السياق
        context_menu.setStyleSheet("""
            QMenu {
                background-color: white;
                border: 1px solid #ddd;
                border-radius: 6px;
                padding: 5px;
                font-size: 12px;
                min-width: 180px;
            }
            QMenu::item {
                padding: 8px 20px;
                border-radius: 4px;
                margin: 2px;
            }
            QMenu::item:selected {
                background-color: #e3f2fd;
                color: #1976d2;
            }
            QMenu::item:disabled {
                color: #666;
                background-color: #f5f5f5;
                font-weight: bold;
            }
            QMenu::separator {
                height: 1px;
                background-color: #eee;
                margin: 5px 10px;
            }
        """)

        # عرض قائمة السياق
        context_menu.exec_(self.status_username.mapToGlobal(position))

    def logout(self):
        """تسجيل خروج المستخدم"""
        # عرض رسالة تأكيد
        reply = QMessageBox.question(
            self,
            "تأكيد تسجيل الخروج",
            "هل أنت متأكد من تسجيل الخروج من النظام؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # إغلاق النافذة الحالية
            self.close()

            # إنشاء نافذة تسجيل دخول جديدة
            from views.login import LoginWindow
            login_window = LoginWindow()

            # متغير لتخزين بيانات المستخدم الجديد
            user_data = [None]

            # ربط إشارة نجاح تسجيل الدخول
            def on_login_successful(user):
                user_data[0] = user

            login_window.loginSuccessful.connect(on_login_successful)

            # عرض نافذة تسجيل الدخول
            login_result = login_window.exec_()

            # التحقق من نتيجة تسجيل الدخول
            if login_result == LoginWindow.Accepted and user_data[0]:
                # إنشاء نافذة رئيسية جديدة مع المستخدم الجديد
                new_window = MainWindow(user_data[0])
                new_window.show()
            else:
                # إذا تم إلغاء تسجيل الدخول، إغلاق التطبيق
                QApplication.quit()

    def apply_styles(self):
        """تطبيق الأنماط على مكونات النافذة"""
        # استخدام التنسيقات من ملف الستايلات
        # تطبيق الأنماط على النافذة الرئيسية وجميع العناصر الفرعية
        self.setStyleSheet(AppStyles.get_main_style())

        # تطبيق الأنماط على جميع الأزرار في التطبيق
        for widget in self.findChildren(QPushButton):
            # الحفاظ على تنسيقات أزرار التنقل الخاصة
            if widget.objectName() != "nav_button":
                # تطبيق الحد الأدنى للعرض والارتفاع على جميع الأزرار
                widget.setMinimumWidth(80)
                widget.setMinimumHeight(24)

    def update_user_info(self):
        """تحديث معلومات المستخدم في شريط الحالة وتحديث حالة أزرار التنقل"""
        if self.current_user and 'username' in self.current_user:
            user_fullname = self.current_user.get('full_name', self.current_user['username'])
            self.status_username.setText(f"المستخدم: {user_fullname}")

            # تحديث حالة تفعيل أزرار التنقل بناءً على صلاحيات المستخدم
            self.update_nav_buttons_state()
        else:
            self.status_username.setText("المستخدم: غير معروف")

    def update_nav_buttons_state(self):
        """تحديث حالة تفعيل أزرار التنقل بناءً على صلاحيات المستخدم"""
        # التحقق من وجود مستخدم حالي
        if not self.current_user or 'id' not in self.current_user:
            return

        # المستخدم admin له جميع الصلاحيات
        if self.current_user.get('username') == 'admin':
            # تفعيل جميع الأزرار
            for button in self.nav_buttons:
                button.setEnabled(True)
            return

        # استيراد وحدة التحكم بالمستخدمين
        from controllers.user_controller import UserController

        user_id = self.current_user['id']

        # تحديث حالة كل زر بناءً على صلاحيات المستخدم
        for i, button in enumerate(self.nav_buttons):
            if i in self.required_permissions:
                permission = self.required_permissions[i]
                has_permission = UserController.check_permission(user_id, permission)

                # تفعيل أو تعطيل الزر بناءً على الصلاحية
                button.setEnabled(has_permission)

                # تغيير لون الزر المعطل ليكون رمادي
                if not has_permission:
                    button.setStyleSheet("color: #999999; background-color: #f0f0f0;")
                else:
                    button.setStyleSheet("")  # إعادة تعيين النمط الافتراضي

    def refresh_all_pages(self):
        """تحديث جميع الصفحات في التطبيق"""
        try:
            # تحديث جميع الصفحات في التطبيق
            for i in range(self.content_widget.count()):
                widget = self.content_widget.widget(i)

                # تحديث الصفحة إذا كانت تحتوي على دالة refresh_page
                if hasattr(widget, 'refresh_page') and callable(widget.refresh_page):
                    widget.refresh_page()

        except Exception as e:
            pass


# Python 3 handles Unicode by default, so no need to manually set encoding
# The following is only kept for documentation purposes:
# import importlib
# importlib.reload(sys)  # Python 3 way to reload modules if needed

if __name__ == "__main__":
    # إنشاء مجلد views إذا لم يكن موجودًا
    os.makedirs("views", exist_ok=True)



    # تعريف معالج الاستثناءات غير المعالجة
    def exception_hook(exctype, value, traceback):
        sys.__excepthook__(exctype, value, traceback)
        if not DEBUG_MODE:
            # طباعة رسالة الخطأ في وحدة التحكم أيضًا
            console_print(f"CRITICAL ERROR: {str(value)}")
            # عرض رسالة خطأ للمستخدم
            error_msg = f"حدث خطأ غير متوقع: {str(value)}\n\nتم حفظ تفاصيل الخطأ في ملف السجل."
            QMessageBox.critical(None, "خطأ غير متوقع", error_msg)

    # تعيين معالج الاستثناءات
    sys.excepthook = exception_hook

    app = QApplication(sys.argv)

    # تثبيت معالج الرسائل المخصص لمنع ظهور رسائل التحذير
    qInstallMessageHandler(suppress_qt_warnings)

    app.setLayoutDirection(Qt.RightToLeft)  # جعل التطبيق للغة العربية من اليمين لليسار

    # تحميل ملف الخط Readex Pro مباشرة من مجلد البرنامج
    try:
        font_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "ReadexPro.ttf")
    except NameError:
        font_path = os.path.join(os.getcwd(), "ReadexPro.ttf")
    if os.path.exists(font_path):
        # Intentar cargar la fuente varias veces para evitar errores
        font_id = -1
        for i in range(3):  # Intentar 3 veces
            font_id = QFontDatabase.addApplicationFont(font_path)
            if font_id != -1:
                break

        if font_id != -1:
            font_families = QFontDatabase.applicationFontFamilies(font_id)
            if font_families:
                custom_font_family = font_families[0]
                # تخزين اسم العائلة في متغير عام لاستخدامه في styles.py
                AppStyles.LOADED_CUSTOM_FONT = custom_font_family

    # تهيئة وتطبيق الخط المناسب للنظام
    system_font = AppStyles.initialize_fonts()
    font = app.font()
    font.setFamily(system_font)
    font.setPointSize(10)
    app.setFont(font)

    # تم تطبيق الخط

    # إنشاء نافذة البداية
    # التحقق من وجود ملف صورة الشاشة البدائية
    splash_file = "ui/splash.png"

    # إذا لم يكن ملف الصورة موجودًا، ننشئ صورة بديلة واضحة
    if not os.path.exists(splash_file):
        # إنشاء صورة بحجم أكبر للوضوح
        splash_pixmap = QPixmap(600, 400)
        # استخدام لون أزرق فاتح للخلفية بدلاً من الأسود
        splash_pixmap.fill(Qt.lightGray)

        # رسم عنوان البرنامج على الصورة
        painter = QPainter(splash_pixmap)
        # استخدام خط أكبر وواضح
        title_font = QFont(system_font, 24, QFont.Bold)
        subtitle_font = QFont(system_font, 14)
        painter.setFont(title_font)

        # رسم عنوان رئيسي باللون الداكن
        painter.setPen(QColor(0, 51, 102))  # لون أزرق داكن
        painter.drawText(splash_pixmap.rect(), Qt.AlignCenter, "Smart Manager")

        # رسم شعار أو نص ثانوي
        painter.setFont(subtitle_font)
        painter.setPen(QColor(60, 60, 60))  # لون رمادي داكن
        subtitle_rect = splash_pixmap.rect()
        subtitle_rect.setTop(subtitle_rect.top() + 100)  # ضبط موضع النص الفرعي
        painter.drawText(subtitle_rect, Qt.AlignCenter, "جاري تحميل النظام...")

        painter.end()
    else:
        # استخدام الصورة الموجودة
        splash_pixmap = QPixmap(splash_file)

    # إنشاء شاشة البداية باستخدام الصورة
    splash = QSplashScreen(splash_pixmap)
    splash.setWindowFlags(Qt.WindowStaysOnTopHint | Qt.FramelessWindowHint)

    # تعيين نمط لرسائل الحالة ليكون أكثر وضوحًا
    message_style = "QSplashScreen { color: #003366; font-weight: bold; font-size: 12pt; }"
    splash.setStyleSheet(message_style)

    splash.show()
    app.processEvents()

    # تهيئة قاعدة البيانات - تحديث النص ليكون واضحًا على الخلفية
    splash.showMessage("جاري تهيئة قاعدة البيانات...", Qt.AlignBottom | Qt.AlignHCenter, QColor(0, 51, 102))
    success, message = DatabaseController.initialize_database()

    if not success:
        QMessageBox.critical(None, "خطأ في تهيئة قاعدة البيانات", message)
        sys.exit(1)

    # إنشاء النافذة الرئيسية - تحديث النص
    splash.showMessage("جاري تحميل التطبيق...", Qt.AlignBottom | Qt.AlignHCenter, QColor(0, 51, 102))

    # إغلاق شاشة البداية
    splash.finish(None)

    # عرض نافذة تسجيل الدخول قبل عرض النافذة الرئيسية
    login_window = LoginWindow()

    # متغير لتخزين بيانات المستخدم بعد تسجيل الدخول
    user_data = [None]  # استخدام قائمة لتخزين المرجع

    # ربط إشارة نجاح تسجيل الدخول بدالة لتخزين بيانات المستخدم
    def on_login_successful(user):
        user_data[0] = user

    # ربط الإشارة بالدالة
    login_window.loginSuccessful.connect(on_login_successful)

    # عرض نافذة تسجيل الدخول وانتظار النتيجة
    print("عرض نافذة تسجيل الدخول...")
    login_result = login_window.exec_()
    print(f"نتيجة تسجيل الدخول: {login_result}")
    print(f"بيانات المستخدم: {user_data[0]}")

    # التحقق من نتيجة تسجيل الدخول
    if login_result != LoginWindow.Accepted or not user_data[0]:
        print("فشل تسجيل الدخول أو تم الإلغاء")
        sys.exit(0)

    # إنشاء النافذة الرئيسية مع بيانات المستخدم
    print("جاري إنشاء النافذة الرئيسية...")
    window = MainWindow(user_data[0])
    print("تم إنشاء النافذة الرئيسية بنجاح")
    window.show()
    print("تم عرض النافذة الرئيسية")

    # تنفيذ التطبيق وإرجاع رمز الخروج
    exit_code = app.exec_()

    # استعادة قنوات الإخراج الأصلية وإغلاق ملف السجل
    if not DEBUG_MODE:
        # استعادة قنوات الإخراج الأصلية
        sys.stdout = original_stdout
        sys.stderr = original_stderr

        # إغلاق ملف السجل
        log_file.close()

    sys.exit(exit_code)