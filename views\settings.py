# This is a test comment to see if file editing works
# إعدادات التطبيق
# ------------------------------
# Settings module for the application

import os
import tempfile
import shutil
import datetime
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTabWidget, QScrollArea, QFormLayout,
    QLabel, QLineEdit, QPushButton, QGroupBox, QFileDialog,
    QTableWidget, QTableWidgetItem, QHeaderView, QFrame, QCheckBox, QSpinBox,
    QMessageBox, QDialog, QComboBox, QGridLayout, QMenu, QAction, QTreeWidget, QTreeWidgetItem
)
from PyQt5.QtCore import Qt, QSettings, QSizeF, QTimer
from PyQt5.QtGui import QFont, QColor
from PyQt5.QtPrintSupport import <PERSON><PERSON>rin<PERSON>, QPrintDialog
from controllers.database_controller import DatabaseController
from controllers.user_controller import UserController
from styles import AppStyles  # استيراد التنسيقات من ملف styles.py

# Define RTL helper classes directly
class RTLComboBox(QComboBox):
    """Replacement class for RTLComboBox"""
    pass

def apply_rtl_to_all_widgets(widget):
    """Replacement function for apply_rtl_to_all_widgets"""
    pass

# إعدادات افتراضية للتطبيق
DEFAULT_SETTINGS = {
    # الإعدادات العامة
    "app_name": "Smart Manager",
    "company_name": "شركتي",
    "company_phone": "01xxxxxxxxx",

    "company_address": "عنوان الشركة",
    "currency": "ج.م",
    "language": "العربية",
    "theme": "فاتح",
    # إعدادات المخزون
    "stock_alert": 5,
    "auto_stock_alert": True,
    # إعدادات الفواتير
    "show_logo_on_invoice": True,
    "invoice_notes": "شكراً لتعاملكم معنا",
    "show_invoice_preview_after_sale": True,
    # إعدادات النظام
    "auto_backup": True,
    "backup_every_days": 7,
    "backup_location": "backups/",
}

class SettingsView(QWidget):
    def __init__(self):
        super().__init__()

        # إنشاء كائن الإعدادات
        self.settings = QSettings("MyCompany", "SmartManager")

        # إعداد مؤقت النسخ الاحتياطي التلقائي
        self.backup_timer = QTimer()
        self.backup_timer.timeout.connect(self.perform_auto_backup)

        # إعداد التخطيط الرئيسي
        layout = QVBoxLayout(self)
        layout.setContentsMargins(25, 25, 25, 25)

        # إضافة عنوان الصفحة
        header_layout = QHBoxLayout()
        page_title = QLabel("الإعدادات")
        page_title.setObjectName("page_title")
        page_title.setFont(QFont("Arial", 20, QFont.Bold))
        header_layout.addWidget(page_title)

        # إضافة زر لحفظ الإعدادات
        self.save_settings_btn = QPushButton("⚙️  حفظ الإعدادات")
        self.save_settings_btn.setFixedSize(180, 40)
        self.save_settings_btn.setObjectName("action_button")
        self.save_settings_btn.setFont(QFont("Arial", 11))
        self.save_settings_btn.setCursor(Qt.PointingHandCursor)
        self.save_settings_btn.clicked.connect(self.save_settings)
        header_layout.addStretch()
        header_layout.addWidget(self.save_settings_btn)

        layout.addLayout(header_layout)

        # إضافة فاصل
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setObjectName("content_separator")
        layout.addWidget(separator)
        layout.addSpacing(20)

        # إنشاء تبويبات الإعدادات
        self.settings_tabs = QTabWidget()
        self.settings_tabs.setObjectName("settings_tabs")
        self.settings_tabs.setLayoutDirection(Qt.RightToLeft)  # تعيين اتجاه التبويبات من اليمين إلى اليسار

        # إنشاء تبويبات مختلفة للإعدادات
        self.general_tab = self.create_general_tab()
        self.users_tab = self.create_users_tab()
        self.database_backup_tab = self.create_database_backup_tab()  # تاب مدمج جديد

        # إضافة التبويبات إلى widget التبويبات
        self.settings_tabs.addTab(self.general_tab, "عام")
        self.settings_tabs.addTab(self.users_tab, "المستخدمين")
        self.settings_tabs.addTab(self.database_backup_tab, "قاعدة البيانات والنسخ الاحتياطي")

        # ربط تغيير التبويب بدالة تحديث المحتوى
        self.settings_tabs.currentChanged.connect(self.on_tab_changed)

        layout.addWidget(self.settings_tabs)

        # تحميل الإعدادات عند بدء التشغيل
        self.load_settings()

        # تطبيق الأنماط
        self.apply_styles()
        # Aplicar configuración RTL a todos los widgets
        apply_rtl_to_all_widgets(self)

        # بدء النسخ الاحتياطي التلقائي إذا كان مفعل
        self.setup_auto_backup()

    def on_tab_changed(self, index):
        """معالجة تغيير التبويب وتحديث المحتوى المناسب"""
        tab_name = self.settings_tabs.tabText(index)

        if tab_name == "عام":
            # يمكن إضافة أي تحديثات مطلوبة للتاب العام
            pass
        elif tab_name == "المستخدمين":
            # يمكن إضافة أي تحديثات مطلوبة لتاب المستخدمين
            pass
        elif tab_name == "قاعدة البيانات والنسخ الاحتياطي":
            # تحديث قائمة النسخ الاحتياطية
            if hasattr(self, 'refresh_backups_list'):
                self.refresh_backups_list()

        print(f"تم تحديث محتوى تاب {tab_name}")

    def create_general_tab(self):
        """إنشاء تبويب الإعدادات العامة"""
        tab = QWidget()
        tab.setLayoutDirection(Qt.RightToLeft)

        # إنشاء التخطيط الرئيسي مباشرة بدون scroll area
        main_layout = QVBoxLayout(tab)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(20)

        # إضافة شريط العنوان
        title_header = self.create_title_header(
            "الإعدادات العامة للنظام",
            "إدارة وتخصيص إعدادات النظام"
        )
        main_layout.addWidget(title_header)

        # إنشاء widget متجاوب للشبكة
        self.responsive_grid_widget = QWidget()
        self.responsive_grid_layout = QGridLayout(self.responsive_grid_widget)
        self.responsive_grid_layout.setContentsMargins(0, 0, 0, 0)
        self.responsive_grid_layout.setSpacing(15)

        # تعيين خصائص التمدد للأعمدة والصفوف
        self.responsive_grid_layout.setColumnStretch(0, 1)  # العمود الأول قابل للتمدد
        self.responsive_grid_layout.setColumnStretch(1, 1)  # العمود الثاني قابل للتمدد
        self.responsive_grid_layout.setRowStretch(0, 1)     # الصف الأول قابل للتمدد
        self.responsive_grid_layout.setRowStretch(1, 1)     # الصف الثاني قابل للتمدد

        # قسم معلومات الشركة
        self.company_group = QGroupBox("معلومات الشركة")
        self.company_group.setMinimumWidth(300)  # حد أدنى للعرض
        company_layout = QFormLayout()
        company_layout.setLabelAlignment(Qt.AlignRight)
        company_layout.setSpacing(12)

        # حقول معلومات الشركة
        self.company_name = QLineEdit()
        self.company_name.setObjectName("search_input")
        self.company_name.setPlaceholderText("أدخل اسم الشركة")
        company_layout.addRow("اسم الشركة:", self.company_name)

        self.company_phone = QLineEdit()
        self.company_phone.setObjectName("search_input")
        self.company_phone.setPlaceholderText("أدخل رقم الهاتف")
        company_layout.addRow("رقم الهاتف:", self.company_phone)



        self.company_address = QLineEdit()
        self.company_address.setObjectName("search_input")
        self.company_address.setPlaceholderText("أدخل عنوان الشركة")
        company_layout.addRow("العنوان:", self.company_address)

        # زر تحميل شعار الشركة
        logo_layout = QHBoxLayout()
        self.company_logo_label = QLabel("لم يتم تحديد شعار")
        self.company_logo_label.setObjectName("field_label")

        upload_logo_btn = QPushButton("تحميل شعار")
        upload_logo_btn.setObjectName("secondary_button")
        upload_logo_btn.clicked.connect(self.upload_logo)

        logo_layout.addWidget(self.company_logo_label)
        logo_layout.addStretch()
        logo_layout.addWidget(upload_logo_btn)

        company_layout.addRow("شعار الشركة:", logo_layout)
        self.company_group.setLayout(company_layout)

        # قسم إعدادات التطبيق
        self.app_group = QGroupBox("إعدادات التطبيق")
        self.app_group.setMinimumWidth(300)  # حد أدنى للعرض
        app_layout = QFormLayout()
        app_layout.setLabelAlignment(Qt.AlignRight)
        app_layout.setSpacing(12)

        # عملة التطبيق
        self.currency_combo = RTLComboBox()
        self.currency_combo.setObjectName("combo_box")
        self.currency_combo.addItems(["ج.م", "$", "€", "£"])
        app_layout.addRow("العملة:", self.currency_combo)

        # لغة التطبيق
        self.language_combo = RTLComboBox()
        self.language_combo.setObjectName("combo_box")
        self.language_combo.addItems(["العربية"])
        app_layout.addRow("اللغة:", self.language_combo)

        # سمة التطبيق
        self.theme_combo = RTLComboBox()
        self.theme_combo.setObjectName("combo_box")
        self.theme_combo.addItems(["فاتح"])
        app_layout.addRow("سمة التطبيق:", self.theme_combo)

        self.app_group.setLayout(app_layout)

        # قسم إعدادات المخزون
        self.inventory_group = QGroupBox("إعدادات المخزون")
        self.inventory_group.setMinimumWidth(300)  # حد أدنى للعرض
        inventory_layout = QFormLayout()
        inventory_layout.setLabelAlignment(Qt.AlignRight)
        inventory_layout.setSpacing(12)

        # حد التنبيه للمخزون
        self.stock_alert_spin = QSpinBox()
        self.stock_alert_spin.setObjectName("search_input")
        self.stock_alert_spin.setRange(1, 100)
        self.stock_alert_spin.setValue(5)
        inventory_layout.addRow("حد التنبيه للمخزون:", self.stock_alert_spin)

        # تفعيل التنبيه التلقائي للمخزون
        self.auto_stock_alert = QCheckBox("تفعيل التنبيه التلقائي عند انخفاض المخزون")
        self.auto_stock_alert.setObjectName("checkbox")
        inventory_layout.addRow("", self.auto_stock_alert)

        self.inventory_group.setLayout(inventory_layout)

        # قسم إعدادات الفواتير
        self.invoice_group = QGroupBox("إعدادات الفواتير")
        self.invoice_group.setMinimumWidth(300)  # حد أدنى للعرض
        invoice_layout = QFormLayout()
        invoice_layout.setLabelAlignment(Qt.AlignRight)
        invoice_layout.setSpacing(12)

        # إظهار شعار الشركة على الفاتورة
        self.show_logo_on_invoice = QCheckBox("إظهار شعار الشركة على الفاتورة")
        self.show_logo_on_invoice.setObjectName("checkbox")
        invoice_layout.addRow("", self.show_logo_on_invoice)

        # عرض معاينة الفاتورة بعد البيع
        self.show_invoice_preview_after_sale = QCheckBox("عرض معاينة الفاتورة بعد البيع لطباعتها")
        self.show_invoice_preview_after_sale.setObjectName("checkbox")
        invoice_layout.addRow("", self.show_invoice_preview_after_sale)

        # ملاحظات الفاتورة
        self.invoice_notes = QLineEdit()
        self.invoice_notes.setObjectName("search_input")
        self.invoice_notes.setPlaceholderText("أدخل ملاحظات إضافية للفاتورة")
        invoice_layout.addRow("ملاحظات الفاتورة:", self.invoice_notes)

        # زر معاينة الفاتورة
        preview_invoice_btn = QPushButton("👁️  معاينة تصميم الفاتورة")
        preview_invoice_btn.setObjectName("secondary_button")
        preview_invoice_btn.setCursor(Qt.PointingHandCursor)
        preview_invoice_btn.clicked.connect(self.preview_invoice_design)
        invoice_layout.addRow("", preview_invoice_btn)

        self.invoice_group.setLayout(invoice_layout)

        # ترتيب الأقسام في شبكة متجاوبة
        self.arrange_responsive_layout()

        # إضافة widget الشبكة المتجاوبة إلى التخطيط الرئيسي
        main_layout.addWidget(self.responsive_grid_widget, 1)  # إعطاء وزن 1 للتمدد

        return tab

    def create_title_header(self, title, subtitle):
        """إنشاء شريط عنوان موحد لجميع التابات"""
        title_container = QWidget()
        title_container.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
                border-bottom: 3px solid #007bff;
                margin-bottom: 10px;
            }
        """)

        title_layout = QVBoxLayout(title_container)
        title_layout.setContentsMargins(20, 15, 20, 15)
        title_layout.setSpacing(5)

        title_label = QLabel(title)
        title_label.setFont(QFont("Arial", 20, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                background: transparent;
                border: none;
            }
        """)

        subtitle_label = QLabel(subtitle)
        subtitle_label.setFont(QFont("Arial", 11))
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setStyleSheet("""
            QLabel {
                color: #6c757d;
                background: transparent;
                border: none;
            }
        """)

        title_layout.addWidget(title_label)
        title_layout.addWidget(subtitle_label)

        return title_container

    def arrange_responsive_layout(self):
        """ترتيب العناصر بشكل متجاوب حسب حجم النافذة"""
        # إزالة جميع العناصر من التخطيط أولاً
        for i in reversed(range(self.responsive_grid_layout.count())):
            self.responsive_grid_layout.itemAt(i).widget().setParent(None)

        # الحصول على عرض النافذة الحالي
        window_width = self.width() if hasattr(self, 'width') else 800

        # تحديد نقطة التحول للتخطيط المتجاوب
        breakpoint = 900  # عرض النافذة الذي يتم عنده التغيير من شبكة إلى عمود

        if window_width < breakpoint:
            # تخطيط عمودي للشاشات الصغيرة (عمود واحد)
            self.responsive_grid_layout.addWidget(self.company_group, 0, 0)
            self.responsive_grid_layout.addWidget(self.app_group, 1, 0)
            self.responsive_grid_layout.addWidget(self.inventory_group, 2, 0)
            self.responsive_grid_layout.addWidget(self.invoice_group, 3, 0)

            # إعادة تعيين خصائص التمدد للعمود الواحد
            self.responsive_grid_layout.setColumnStretch(0, 1)
            self.responsive_grid_layout.setColumnStretch(1, 0)
        else:
            # تخطيط شبكي للشاشات الكبيرة (2x2)
            self.responsive_grid_layout.addWidget(self.company_group, 0, 0)
            self.responsive_grid_layout.addWidget(self.app_group, 0, 1)
            self.responsive_grid_layout.addWidget(self.inventory_group, 1, 0)
            self.responsive_grid_layout.addWidget(self.invoice_group, 1, 1)

            # إعادة تعيين خصائص التمدد للشبكة
            self.responsive_grid_layout.setColumnStretch(0, 1)
            self.responsive_grid_layout.setColumnStretch(1, 1)

    def resizeEvent(self, event):
        """التعامل مع تغيير حجم النافذة"""
        super().resizeEvent(event)
        # إعادة ترتيب التخطيط عند تغيير حجم النافذة
        if hasattr(self, 'responsive_grid_layout'):
            self.arrange_responsive_layout()
        # إعادة ترتيب التخطيط للتاب المدمج
        if hasattr(self, 'database_backup_grid_layout'):
            self.arrange_database_backup_layout()

    def create_database_backup_tab(self):
        """إنشاء تاب مدمج لقاعدة البيانات والنسخ الاحتياطي"""
        tab = QWidget()
        tab.setLayoutDirection(Qt.RightToLeft)

        # إنشاء التخطيط الرئيسي مباشرة بدون scroll area
        main_layout = QVBoxLayout(tab)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(20)

        # إضافة شريط العنوان
        title_header = self.create_title_header(
            "قاعدة البيانات والنسخ الاحتياطي",
            ""
        )
        main_layout.addWidget(title_header)

        # إنشاء widget متجاوب للشبكة
        self.database_backup_grid_widget = QWidget()
        self.database_backup_grid_layout = QGridLayout(self.database_backup_grid_widget)
        self.database_backup_grid_layout.setContentsMargins(0, 0, 0, 0)
        self.database_backup_grid_layout.setSpacing(15)

        # تعيين خصائص التمدد للأعمدة والصفوف
        self.database_backup_grid_layout.setColumnStretch(0, 1)  # العمود الأول قابل للتمدد
        self.database_backup_grid_layout.setColumnStretch(1, 1)  # العمود الثاني قابل للتمدد
        self.database_backup_grid_layout.setRowStretch(0, 1)     # الصف الأول قابل للتمدد
        self.database_backup_grid_layout.setRowStretch(1, 1)     # الصف الثاني قابل للتمدد

        # قسم إعدادات قاعدة البيانات
        self.database_group = self.create_database_section()
        self.database_group.setMinimumWidth(350)

        # قسم نقل البيانات
        self.export_group = self.create_export_section()
        self.export_group.setMinimumWidth(350)

        # قسم إدارة النسخ الاحتياطية
        self.backup_management_group = self.create_backup_management_section()
        self.backup_management_group.setMinimumWidth(350)

        # ترتيب الأقسام في شبكة متجاوبة
        self.arrange_database_backup_layout()

        # إضافة widget الشبكة المتجاوبة إلى التخطيط الرئيسي
        main_layout.addWidget(self.database_backup_grid_widget, 1)  # إعطاء وزن 1 للتمدد

        return tab

    def arrange_database_backup_layout(self):
        """ترتيب العناصر بشكل متجاوب حسب حجم النافذة للتاب المدمج"""
        # إزالة جميع العناصر من التخطيط أولاً
        for i in reversed(range(self.database_backup_grid_layout.count())):
            self.database_backup_grid_layout.itemAt(i).widget().setParent(None)

        # الحصول على عرض النافذة الحالي
        window_width = self.width() if hasattr(self, 'width') else 800

        # تحديد نقطة التحول للتخطيط المتجاوب
        breakpoint = 900  # عرض أصغر قليلاً للتاب المدمج (3 أقسام)

        if window_width < breakpoint:
            # تخطيط عمودي للشاشات الصغيرة (عمود واحد)
            self.database_backup_grid_layout.addWidget(self.database_group, 0, 0)
            self.database_backup_grid_layout.addWidget(self.export_group, 1, 0)
            self.database_backup_grid_layout.addWidget(self.backup_management_group, 2, 0)

            # إعادة تعيين خصائص التمدد للعمود الواحد
            self.database_backup_grid_layout.setColumnStretch(0, 1)
            self.database_backup_grid_layout.setColumnStretch(1, 0)
        else:
            # تخطيط مختلط للشاشات الكبيرة
            # الصف الأول: قسم قاعدة البيانات (يأخذ العمود الأول)
            # الصف الأول: قسم نقل البيانات (يأخذ العمود الثاني)
            # الصف الثاني: قسم إدارة النسخ الاحتياطية (يأخذ العمودين - عرض كامل)
            self.database_backup_grid_layout.addWidget(self.database_group, 0, 0)
            self.database_backup_grid_layout.addWidget(self.export_group, 0, 1)
            self.database_backup_grid_layout.addWidget(self.backup_management_group, 1, 0, 1, 2)  # يمتد عبر عمودين

            # إعادة تعيين خصائص التمدد للشبكة
            self.database_backup_grid_layout.setColumnStretch(0, 1)
            self.database_backup_grid_layout.setColumnStretch(1, 1)

    def create_database_section(self):
        """إنشاء قسم إعدادات قاعدة البيانات والنسخ الاحتياطي التلقائي"""
        group = QGroupBox("إعدادات قاعدة البيانات والنسخ الاحتياطي")
        layout = QFormLayout()
        layout.setLabelAlignment(Qt.AlignRight)
        layout.setSpacing(12)

        # مسار قاعدة البيانات مع زر اختبار الاتصال
        db_path_layout = QHBoxLayout()
        self.db_path_input = QLineEdit()
        self.db_path_input.setObjectName("search_input")
        self.db_path_input.setPlaceholderText("مسار ملف قاعدة البيانات")
        self.db_path_input.setText("database/store.db")

        test_connection_btn = QPushButton("🔗  اختبار الاتصال")
        test_connection_btn.setObjectName("secondary_button")
        test_connection_btn.setMinimumWidth(100)  # حد أدنى للعرض
        test_connection_btn.setMaximumWidth(150)  # حد أقصى للعرض
        test_connection_btn.clicked.connect(self.test_database_connection)

        # تعيين نسب التمدد: حقل النص يأخذ 70% والزر 30%
        db_path_layout.addWidget(self.db_path_input, 7)
        db_path_layout.addWidget(test_connection_btn, 3)

        layout.addRow("مسار قاعدة البيانات:", db_path_layout)

        # إضافة فاصل بصري
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setStyleSheet("QFrame { color: #ddd; margin: 10px 0; }")
        layout.addRow("", separator)

        # تفعيل النسخ الاحتياطي التلقائي
        self.auto_backup_enabled = QCheckBox("تفعيل النسخ الاحتياطي التلقائي")
        self.auto_backup_enabled.setObjectName("checkbox")
        self.auto_backup_enabled.setChecked(True)
        self.auto_backup_enabled.stateChanged.connect(self.on_auto_backup_changed)
        layout.addRow("", self.auto_backup_enabled)

        # تكرار النسخ الاحتياطي
        self.backup_frequency = RTLComboBox()
        self.backup_frequency.setObjectName("combo_box")
        self.backup_frequency.addItems(["يومياً", "أسبوعياً", "شهرياً"])
        self.backup_frequency.setCurrentText("أسبوعياً")
        layout.addRow("تكرار النسخ:", self.backup_frequency)

        # مجلد النسخ الاحتياطي
        backup_folder_layout = QHBoxLayout()
        self.backup_folder_input = QLineEdit()
        self.backup_folder_input.setObjectName("search_input")
        self.backup_folder_input.setPlaceholderText("مجلد النسخ الاحتياطي")
        self.backup_folder_input.setText("backups/")

        browse_folder_btn = QPushButton("📁  استعراض")
        browse_folder_btn.setObjectName("secondary_button")
        browse_folder_btn.setMinimumWidth(80)   # حد أدنى للعرض
        browse_folder_btn.setMaximumWidth(120)  # حد أقصى للعرض
        browse_folder_btn.setToolTip("اختيار مجلد النسخ الاحتياطي")
        browse_folder_btn.clicked.connect(self.browse_backup_folder)

        # تعيين نسب التمدد: حقل النص يأخذ 75% والزر 25%
        backup_folder_layout.addWidget(self.backup_folder_input, 7)
        backup_folder_layout.addWidget(browse_folder_btn, 2)

        layout.addRow("مجلد النسخ:", backup_folder_layout)

        # عدد النسخ المحتفظ بها
        self.max_backups = QSpinBox()
        self.max_backups.setObjectName("search_input")
        self.max_backups.setRange(1, 50)
        self.max_backups.setValue(5)
        self.max_backups.setSuffix(" نسخة")
        layout.addRow("عدد النسخ المحتفظ بها:", self.max_backups)

        # ضغط النسخ الاحتياطية
        self.compress_backups = QCheckBox("ضغط ملفات النسخ الاحتياطي")
        self.compress_backups.setObjectName("checkbox")
        self.compress_backups.setChecked(True)
        layout.addRow("", self.compress_backups)

        # تحديث حالة العناصر بناءً على حالة التفعيل
        self.on_auto_backup_changed(self.auto_backup_enabled.checkState())

        group.setLayout(layout)
        return group

    def create_export_section(self):
        """إنشاء قسم نقل البيانات"""
        group = QGroupBox("نقل البيانات والنسخ الاحتياطي")
        layout = QVBoxLayout()
        layout.setSpacing(12)

        # معلومات القسم
        info_label = QLabel("تصدير واستيراد البيانات وإنشاء النسخ الاحتياطية")
        info_label.setObjectName("hint_label")
        info_label.setWordWrap(True)
        layout.addWidget(info_label)

        # أزرار التصدير والاستيراد
        buttons_layout = QGridLayout()

        export_products_btn = QPushButton("📤  تصدير المنتجات")
        export_products_btn.setObjectName("secondary_button")
        export_products_btn.clicked.connect(self.export_products_data)

        export_customers_btn = QPushButton("📤  تصدير العملاء")
        export_customers_btn.setObjectName("secondary_button")
        export_customers_btn.clicked.connect(self.export_customers_data)

        import_products_btn = QPushButton("📥  استيراد المنتجات")
        import_products_btn.setObjectName("action_button")
        import_products_btn.clicked.connect(self.import_products_data)

        import_customers_btn = QPushButton("📥  استيراد العملاء")
        import_customers_btn.setObjectName("action_button")
        import_customers_btn.clicked.connect(self.import_customers_data)

        # أزرار النسخ الاحتياطي (منقولة من قسم إدارة النسخ الاحتياطية)
        create_backup_btn = QPushButton("💾  إنشاء نسخة احتياطية")
        create_backup_btn.setObjectName("action_button")
        create_backup_btn.clicked.connect(self.create_manual_backup)

        restore_backup_btn = QPushButton("🔄  استعادة نسخة احتياطية")
        restore_backup_btn.setObjectName("secondary_button")
        restore_backup_btn.clicked.connect(self.restore_backup)

        buttons_layout.addWidget(export_products_btn, 0, 0)
        buttons_layout.addWidget(export_customers_btn, 0, 1)
        buttons_layout.addWidget(import_products_btn, 1, 0)
        buttons_layout.addWidget(import_customers_btn, 1, 1)
        buttons_layout.addWidget(create_backup_btn, 2, 0)  # العمود الأول
        buttons_layout.addWidget(restore_backup_btn, 2, 1)  # العمود الثاني

        layout.addLayout(buttons_layout)
        group.setLayout(layout)
        return group

    def on_auto_backup_changed(self, state):
        """تحديث حالة عناصر النسخ الاحتياطي التلقائي بناءً على حالة التفعيل"""
        enabled = state == Qt.Checked

        # تفعيل/تعطيل العناصر المرتبطة بالنسخ الاحتياطي التلقائي
        if hasattr(self, 'backup_frequency'):
            self.backup_frequency.setEnabled(enabled)
        if hasattr(self, 'backup_folder_input'):
            self.backup_folder_input.setEnabled(enabled)
        if hasattr(self, 'max_backups'):
            self.max_backups.setEnabled(enabled)
        if hasattr(self, 'compress_backups'):
            self.compress_backups.setEnabled(enabled)

        # إعادة إعداد النسخ الاحتياطي التلقائي
        self.setup_auto_backup()

    def create_backup_management_section(self):
        """إنشاء قسم إدارة النسخ الاحتياطية"""
        group = QGroupBox("إدارة النسخ الاحتياطية")
        layout = QVBoxLayout()
        layout.setSpacing(12)

        # جدول النسخ الاحتياطية
        self.backups_table = QTableWidget()
        self.backups_table.setColumnCount(3)  # بدون عمود الإجراءات
        self.backups_table.setHorizontalHeaderLabels(["اسم الملف", "تاريخ الإنشاء", "الحجم"])

        # ضبط خصائص الجدول
        self.backups_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.backups_table.verticalHeader().setVisible(False)
        self.backups_table.setAlternatingRowColors(True)
        self.backups_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.backups_table.setSelectionBehavior(QTableWidget.SelectRows)

        # تفعيل قائمة السياق
        self.backups_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.backups_table.customContextMenuRequested.connect(self.show_backup_context_menu)

        # تحميل النسخ الاحتياطية الفعلية من المجلد
        # سيتم استدعاء refresh_backups_list() لاحقاً عند تحديث التاب

        layout.addWidget(self.backups_table)
        group.setLayout(layout)
        return group

    def create_users_tab(self):
        """إنشاء تبويب إعدادات المستخدمين"""
        tab = QWidget()
        tab.setLayoutDirection(Qt.RightToLeft)

        # إنشاء التخطيط الرئيسي مباشرة بدون scroll area
        main_layout = QVBoxLayout(tab)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(20)

        # إضافة شريط العنوان
        title_header = self.create_title_header(
            "إدارة المستخدمين والصلاحيات",
            "إدارة حسابات المستخدمين وتحديد صلاحياتهم"
        )
        main_layout.addWidget(title_header)

        # قسم المستخدم الحالي - يفترض أن هناك نظام تسجيل دخول يحفظ معلومات المستخدم الحالي
        # هنا نعرض معلومات المستخدم admin افتراضياً
        current_user_group = QGroupBox("المستخدم الحالي")
        current_user_layout = QFormLayout()
        current_user_layout.setLabelAlignment(Qt.AlignRight)
        current_user_layout.setSpacing(12)

        # معلومات المستخدم
        self.current_user_name = QLabel("المدير")
        self.current_user_name.setFont(QFont("Arial", 10, QFont.Bold))
        current_user_layout.addRow("اسم المستخدم:", self.current_user_name)

        self.current_user_role = QLabel("مدير النظام")
        self.current_user_role.setStyleSheet("color: #3b82f6; font-weight: bold;")
        current_user_layout.addRow("الصلاحية:", self.current_user_role)

        # أزرار تغيير كلمة المرور وتسجيل الخروج
        buttons_layout = QHBoxLayout()

        change_password_btn = QPushButton("تغيير كلمة المرور")
        change_password_btn.setObjectName("secondary_button")
        change_password_btn.clicked.connect(self.change_current_password)

        logout_btn = QPushButton("تسجيل الخروج")
        logout_btn.setObjectName("danger_button")
        logout_btn.clicked.connect(self.logout_user)

        buttons_layout.addWidget(change_password_btn)
        buttons_layout.addWidget(logout_btn)
        buttons_layout.addStretch()

        current_user_layout.addRow("", buttons_layout)

        current_user_group.setLayout(current_user_layout)
        main_layout.addWidget(current_user_group)

        # قسم إدارة المستخدمين
        users_group = QGroupBox("إدارة المستخدمين")
        users_layout = QVBoxLayout()

        # جدول المستخدمين
        self.users_table = QTableWidget()
        self.users_table.setColumnCount(4)
        self.users_table.setHorizontalHeaderLabels(["اسم المستخدم", "الاسم الكامل", "الصلاحية", "الحالة"])

        # ضبط خصائص الجدول
        self.users_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.users_table.verticalHeader().setVisible(False)
        self.users_table.setAlternatingRowColors(True)
        self.users_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.users_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.users_table.setContextMenuPolicy(Qt.CustomContextMenu)  # تمكين قائمة السياق المخصصة
        self.users_table.customContextMenuRequested.connect(self.show_users_context_menu)  # ربط حدث طلب قائمة السياق

        # تحميل بيانات المستخدمين من قاعدة البيانات
        self.load_users_data()

        users_layout.addWidget(self.users_table)

        # أزرار إدارة المستخدمين
        user_buttons_layout = QHBoxLayout()

        self.add_user_btn = QPushButton("➕  إضافة مستخدم جديد")
        self.add_user_btn.setObjectName("action_button")
        self.add_user_btn.clicked.connect(self.add_user)

        self.edit_user_btn = QPushButton("✏️  تعديل المستخدم المحدد")
        self.edit_user_btn.setObjectName("secondary_button")
        self.edit_user_btn.clicked.connect(self.edit_user)

        self.delete_user_btn = QPushButton("❌  حذف المستخدم المحدد")
        self.delete_user_btn.setObjectName("danger_button")
        self.delete_user_btn.clicked.connect(self.delete_user)

        user_buttons_layout.addWidget(self.add_user_btn)
        user_buttons_layout.addWidget(self.edit_user_btn)
        user_buttons_layout.addWidget(self.delete_user_btn)
        user_buttons_layout.addStretch()

        users_layout.addLayout(user_buttons_layout)
        users_group.setLayout(users_layout)
        main_layout.addWidget(users_group, 1)  # إعطاء وزن 1 للتمدد

        return tab

    def load_users_data(self):
        """تحميل بيانات المستخدمين من قاعدة البيانات"""
        try:
            # تفريغ الجدول أولاً
            self.users_table.setRowCount(0)

            # الحصول على بيانات المستخدمين من قاعدة البيانات
            users = UserController.get_all_users()

            # ملء الجدول بالبيانات
            for row, user in enumerate(users):
                self.users_table.insertRow(row)
                self.users_table.setItem(row, 0, QTableWidgetItem(user['username']))
                self.users_table.setItem(row, 1, QTableWidgetItem(user['full_name']))
                self.users_table.setItem(row, 2, QTableWidgetItem(user['role']))

                status_item = QTableWidgetItem(user['status'])
                if user['is_active']:
                    status_item.setForeground(QColor("#27ae60"))  # لون أخضر للمستخدمين النشطين
                else:
                    status_item.setForeground(QColor("#e74c3c"))  # لون أحمر للمستخدمين غير النشطين
                self.users_table.setItem(row, 3, status_item)

                # تخزين معرف المستخدم كبيانات إضافية
                self.users_table.item(row, 0).setData(Qt.UserRole, user['id'])

            print(f"تم تحميل {len(users)} مستخدم من قاعدة البيانات")
        except Exception as e:
            QMessageBox.critical(self, "خطأ في تحميل البيانات", f"حدث خطأ أثناء تحميل بيانات المستخدمين: {str(e)}")

    def load_permissions_data(self):
        """تحميل بيانات صلاحيات الأدوار من قاعدة البيانات"""
        try:
            # تفريغ الجدول أولاً
            self.permissions_table.setRowCount(0)

            # الحصول على بيانات الصلاحيات من قاعدة البيانات
            permissions = UserController.get_role_permissions()

            # الصلاحيات المتاحة - مجموعة بحسب الوظائف
            available_permissions = {
                "المبيعات": [
                    "عرض المبيعات",
                    "إضافة عملية بيع",
                    "تعديل عملية بيع",
                    "حذف عملية بيع",
                    "طباعة فاتورة مبيعات"
                ],
                "المنتجات": [
                    "عرض المنتجات",
                    "إضافة منتج",
                    "تعديل منتج",
                    "حذف منتج",
                    "إدارة فئات المنتجات",
                    "تعديل أسعار المنتجات"
                ],
                "المخزون": [
                    "عرض المخزون",
                    "إضافة للمخزون",
                    "تعديل المخزون",
                    "جرد المخزون"
                ],
                "الفواتير": [
                    "عرض الفواتير",
                    "إلغاء فاتورة",
                    "طباعة فاتورة",
                    "تعديل فاتورة"
                ],
                "التقارير": [
                    "عرض تقارير المبيعات",
                    "عرض تقارير المخزون",
                    "عرض تقارير الأرباح",
                    "عرض تقارير العملاء",
                    "عرض تقارير الموردين",
                    "عرض تقرير المنتجات الأكثر مبيعاً",
                    "تصدير التقارير"
                ],
                "العملاء": [
                    "عرض العملاء",
                    "إضافة عميل",
                    "تعديل عميل",
                    "حذف عميل",
                    "إدارة ديون العملاء",
                    "عرض تفاصيل العميل"
                ],
                "الموردين": [
                    "عرض الموردين",
                    "إضافة مورد",
                    "تعديل مورد",
                    "حذف مورد",
                    "إدارة مدفوعات الموردين",
                    "عرض تفاصيل المورد"
                ],
                "المصروفات": [
                    "عرض المصروفات",
                    "إضافة مصروف",
                    "تعديل مصروف",
                    "حذف مصروف"
                ],
                "المستخدمين": [
                    "عرض المستخدمين",
                    "إضافة مستخدم",
                    "تعديل مستخدم",
                    "حذف مستخدم",
                    "إدارة صلاحيات المستخدمين"
                ],
                "الإعدادات": [
                    "عرض الإعدادات",
                    "تعديل إعدادات النظام",
                    "إدارة النسخ الاحتياطي",
                    "استعادة النسخ الاحتياطي"
                ]
            }

            # إعادة تهيئة جدول الصلاحيات
            self.permissions_table.clear()
            self.permissions_table.setColumnCount(4)
            self.permissions_table.setHorizontalHeaderLabels(["الصلاحية", "مدير", "كاشير", "مدير مخزون"])

            # تتبع الصف الحالي
            current_row = 0

            # ملء الجدول بالبيانات المجمعة حسب الفئات
            for category, perms in available_permissions.items():
                # إضافة صف لعنوان الفئة
                self.permissions_table.insertRow(current_row)
                category_item = QTableWidgetItem(category)
                category_item.setBackground(QColor("#f1f5f9"))  # لون خلفية فاتح للفئة
                font = category_item.font()
                font.setBold(True)
                category_item.setFont(font)
                self.permissions_table.setItem(current_row, 0, category_item)

                # إضافة زر "تحديد الكل" للفئة
                for col, role in enumerate(["مدير", "كاشير", "مدير مخزون"]):
                    # إنشاء محتوى مخصص لوضع الـ checkbox في وسط الخلية
                    cell_widget = QWidget()
                    cell_layout = QHBoxLayout(cell_widget)
                    cell_layout.setAlignment(Qt.AlignCenter)
                    cell_layout.setContentsMargins(0, 0, 0, 0)

                    # إنشاء checkbox للفئة
                    category_checkbox = QCheckBox()

                    # تعيين حالة الـ checkbox بناءً على البيانات المستلمة
                    # المدير دائماً لديه كل الصلاحيات
                    is_checked = True if role == "مدير" else False

                    # تعطيل التعديل للمدير (يجب أن يكون لديه كل الصلاحيات)
                    if role == "مدير":
                        category_checkbox.setEnabled(False)
                    else:
                        # ربط الـ checkbox بوظيفة تغيير صلاحيات الفئة
                        from functools import partial
                        category_checkbox.stateChanged.connect(
                            partial(self.on_category_permission_changed, category, current_row, col+1)
                        )

                    category_checkbox.setChecked(is_checked)
                    cell_layout.addWidget(category_checkbox)
                    self.permissions_table.setCellWidget(current_row, col + 1, cell_widget)

                current_row += 1

                # إضافة صفوف للصلاحيات الفرعية
                for perm_name in perms:
                    self.permissions_table.insertRow(current_row)
                    perm_item = QTableWidgetItem("    " + perm_name)  # إضافة مسافات للإزاحة
                    self.permissions_table.setItem(current_row, 0, perm_item)

                    # إنشاء خلايا للصلاحيات
                    for col, role in enumerate(["مدير", "كاشير", "مدير مخزون"]):
                        checkbox = QCheckBox()
                        # تعيين حالة الـ checkbox بناءً على البيانات المستلمة
                        is_checked = True if role == "مدير" else False

                        if role in permissions and perm_name in permissions[role]:
                            is_checked = permissions[role][perm_name]

                        checkbox.setChecked(is_checked)

                        # تعطيل التعديل للمدير (يجب أن يكون لديه كل الصلاحيات)
                        if role == "مدير":
                            checkbox.setEnabled(False)
                        else:
                            # ربط الـ checkbox بوظيفة تغيير الصلاحيات
                            # استخدام functools.partial لتجنب مشاكل الـ lambda مع الحلقات
                            from functools import partial
                            checkbox.stateChanged.connect(
                                partial(self.on_permission_changed, current_row, col+1, perm_name, category)
                            )

                        # إنشاء محتوى مخصص لوضع الـ checkbox في وسط الخلية
                        cell_widget = QWidget()
                        cell_layout = QHBoxLayout(cell_widget)
                        cell_layout.setAlignment(Qt.AlignCenter)
                        cell_layout.setContentsMargins(0, 0, 0, 0)
                        cell_layout.addWidget(checkbox)

                        self.permissions_table.setCellWidget(current_row, col + 1, cell_widget)

                    current_row += 1

            print("تم تحميل بيانات الصلاحيات")
        except Exception as e:
            QMessageBox.critical(self, "خطأ في تحميل البيانات", f"حدث خطأ أثناء تحميل بيانات الصلاحيات: {str(e)}")

    def change_current_password(self):
        """تغيير كلمة مرور المستخدم الحالي"""
        try:
            # في هذا المثال نفترض أن المستخدم الحالي هو admin
            # في التطبيق الحقيقي يجب استخدام معرف المستخدم الحالي من نظام تسجيل الدخول
            dialog = QDialog(self)
            dialog.setWindowTitle("تغيير كلمة المرور")
            dialog.setMinimumWidth(400)

            # تخطيط النموذج
            form_layout = QFormLayout(dialog)

            # حقول كلمة المرور
            old_password = QLineEdit()
            old_password.setObjectName("search_input")
            old_password.setPlaceholderText("أدخل كلمة المرور الحالية")
            old_password.setEchoMode(QLineEdit.Password)

            new_password = QLineEdit()
            new_password.setObjectName("search_input")
            new_password.setPlaceholderText("أدخل كلمة المرور الجديدة")
            new_password.setEchoMode(QLineEdit.Password)

            confirm_password = QLineEdit()
            confirm_password.setObjectName("search_input")
            confirm_password.setPlaceholderText("تأكيد كلمة المرور الجديدة")
            confirm_password.setEchoMode(QLineEdit.Password)

            # إضافة الحقول إلى التخطيط
            form_layout.addRow("كلمة المرور الحالية:", old_password)
            form_layout.addRow("كلمة المرور الجديدة:", new_password)
            form_layout.addRow("تأكيد كلمة المرور:", confirm_password)

            # أزرار الإجراءات
            button_box = QHBoxLayout()
            save_btn = QPushButton("تغيير كلمة المرور")
            save_btn.setObjectName("action_button")

            cancel_btn = QPushButton("إلغاء")
            cancel_btn.setObjectName("secondary_button")

            button_box.addWidget(save_btn)
            button_box.addWidget(cancel_btn)

            form_layout.addRow("", button_box)

            # ربط الأزرار بالوظائف
            cancel_btn.clicked.connect(dialog.reject)

            # وظيفة تغيير كلمة المرور
            def change_password():
                # التحقق من الإدخال
                if not old_password.text() or not new_password.text() or not confirm_password.text():
                    QMessageBox.warning(dialog, "بيانات غير مكتملة", "يرجى ملء جميع الحقول")
                    return

                # التحقق من تطابق كلمة المرور الجديدة
                if new_password.text() != confirm_password.text():
                    QMessageBox.warning(dialog, "كلمات المرور غير متطابقة", "كلمة المرور الجديدة وتأكيدها غير متطابقين")
                    return

                # تغيير كلمة المرور - افتراضياً نستخدم المستخدم الأول (admin)
                admin_user = UserController.get_user_info(username="admin")
                if admin_user:
                    success, message = UserController.change_password(
                        admin_user['id'], old_password.text(), new_password.text()
                    )

                    if success:
                        QMessageBox.information(dialog, "تم تغيير كلمة المرور", message)
                        dialog.accept()
                    else:
                        QMessageBox.warning(dialog, "خطأ", message)
                else:
                    QMessageBox.warning(dialog, "خطأ", "لم يتم العثور على المستخدم")

            save_btn.clicked.connect(change_password)

            # عرض الحوار
            dialog.exec_()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تغيير كلمة المرور: {str(e)}")

    def logout_user(self):
        """تسجيل خروج المستخدم"""
        reply = QMessageBox.question(
            self,
            "تسجيل الخروج",
            "هل أنت متأكد من رغبتك بتسجيل الخروج؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            QMessageBox.information(self, "تسجيل الخروج", "تم تسجيل الخروج بنجاح")

            # استيراد المكتبات اللازمة
            from views.login import LoginWindow
            from PyQt5.QtWidgets import QApplication

            # الحصول على النافذة الرئيسية
            main_window = self.window()

            # إخفاء النافذة الرئيسية
            main_window.hide()

            # إنشاء وعرض نافذة تسجيل الدخول
            login_window = LoginWindow()

            # إذا تم تسجيل الدخول بنجاح، أعد فتح النافذة الرئيسية
            if login_window.exec_() == LoginWindow.Accepted:
                # تحديث بيانات المستخدم في النافذة الرئيسية
                main_window.current_user = login_window.user_data
                main_window.update_user_info()
                main_window.show()
            else:
                # إغلاق التطبيق إذا تم إلغاء تسجيل الدخول
                import sys
                QApplication.quit()
                sys.exit(0)

    def browse_db_file(self):
        """اختيار مسار ملف قاعدة البيانات"""
        file_name, _ = QFileDialog.getSaveFileName(
            self,
            "اختر مسار ملف قاعدة البيانات",
            "",
            "قواعد بيانات SQLite (*.db *.sqlite);;كل الملفات (*.*)"
        )
        if file_name:
            self.db_path_input.setText(file_name)

    def test_connection(self):
        """اختبار الاتصال بقاعدة البيانات"""
        # جمع إعدادات الاتصال الحالية من واجهة المستخدم
        db_settings = {
            "db_type": "sqlite",
            "db_path": self.db_path_input.text(),
            "db_host": "",
            "db_port": "",
            "db_name": "",
            "db_user": "",
            "db_password": ""
        }

        try:
            # استخدام المتحكم لاختبار الاتصال
            db_controller = DatabaseController()
            success, message = db_controller.test_connection(db_settings)

            if success:
                QMessageBox.information(
                    self,
                    "نجاح الاتصال",
                    f"تم الاتصال بقاعدة البيانات SQLite بنجاح.\n\n{message}"
                )
            else:
                QMessageBox.critical(
                    self,
                    "فشل الاتصال",
                    f"فشل الاتصال بقاعدة البيانات: {message}"
                )
        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ أثناء محاولة الاتصال: {str(e)}"
            )

    def test_database_connection(self):
        """اختبار الاتصال بقاعدة البيانات"""
        # جمع إعدادات الاتصال الحالية من واجهة المستخدم
        db_settings = {
            "db_type": "sqlite",
            "db_path": self.db_path_input.text(),
            "db_host": "",
            "db_port": "",
            "db_name": "",
            "db_user": "",
            "db_password": ""
        }

        try:
            # استخدام المتحكم لاختبار الاتصال
            db_controller = DatabaseController()
            success, message = db_controller.test_connection(db_settings)

            if success:
                QMessageBox.information(
                    self,
                    "نجاح الاتصال",
                    f"تم الاتصال بقاعدة البيانات SQLite بنجاح.\n\n{message}"
                )
            else:
                QMessageBox.critical(
                    self,
                    "فشل الاتصال",
                    f"فشل الاتصال بقاعدة البيانات: {message}"
                )
        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ أثناء محاولة الاتصال: {str(e)}"
            )

    def export_products_data(self):
        """تصدير بيانات المنتجات إلى CSV"""
        file_name, _ = QFileDialog.getSaveFileName(
            self,
            "حفظ ملف المنتجات",
            "products.csv",
            "ملفات CSV (*.csv);;كل الملفات (*.*)"
        )
        if file_name:
            QMessageBox.information(
                self,
                "تم التصدير",
                f"تم تصدير بيانات المنتجات إلى:\n{file_name}"
            )

    def export_customers_data(self):
        """تصدير بيانات العملاء إلى CSV"""
        file_name, _ = QFileDialog.getSaveFileName(
            self,
            "حفظ ملف العملاء",
            "customers.csv",
            "ملفات CSV (*.csv);;كل الملفات (*.*)"
        )
        if file_name:
            QMessageBox.information(
                self,
                "تم التصدير",
                f"تم تصدير بيانات العملاء إلى:\n{file_name}"
            )

    def import_products_data(self):
        """استيراد بيانات المنتجات من CSV"""
        file_name, _ = QFileDialog.getOpenFileName(
            self,
            "اختر ملف المنتجات",
            "",
            "ملفات CSV (*.csv);;كل الملفات (*.*)"
        )
        if file_name:
            reply = QMessageBox.question(
                self,
                "تأكيد الاستيراد",
                f"هل تريد استيراد بيانات المنتجات من:\n{file_name}\n\nسيتم دمج البيانات مع البيانات الموجودة.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            if reply == QMessageBox.Yes:
                QMessageBox.information(
                    self,
                    "تم الاستيراد",
                    "تم استيراد بيانات المنتجات بنجاح!"
                )

    def import_customers_data(self):
        """استيراد بيانات العملاء من CSV"""
        file_name, _ = QFileDialog.getOpenFileName(
            self,
            "اختر ملف العملاء",
            "",
            "ملفات CSV (*.csv);;كل الملفات (*.*)"
        )
        if file_name:
            reply = QMessageBox.question(
                self,
                "تأكيد الاستيراد",
                f"هل تريد استيراد بيانات العملاء من:\n{file_name}\n\nسيتم دمج البيانات مع البيانات الموجودة.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            if reply == QMessageBox.Yes:
                QMessageBox.information(
                    self,
                    "تم الاستيراد",
                    "تم استيراد بيانات العملاء بنجاح!"
                )

    def browse_backup_folder(self):
        """اختيار مجلد النسخ الاحتياطي"""
        dir_path = QFileDialog.getExistingDirectory(
            self,
            "اختر مجلد النسخ الاحتياطي",
            ""
        )
        if dir_path:
            self.backup_folder_input.setText(dir_path)

    def create_manual_backup(self):
        """إنشاء نسخة احتياطية يدوياً"""
        try:
            import os
            import shutil
            from datetime import datetime

            # الحصول على مسار قاعدة البيانات الحالية
            current_db_path = self.db_path_input.text() or "database/store.db"

            # التحقق من وجود ملف قاعدة البيانات
            if not os.path.exists(current_db_path):
                QMessageBox.warning(
                    self,
                    "خطأ",
                    f"لا يمكن العثور على ملف قاعدة البيانات:\n{current_db_path}"
                )
                return

            # الحصول على مسار مجلد النسخ الاحتياطي
            backup_folder = self.backup_folder_input.text() or "backups"

            # إنشاء مجلد النسخ الاحتياطي إذا لم يكن موجوداً
            if not os.path.exists(backup_folder):
                os.makedirs(backup_folder)

            # إنشاء اسم الملف بناءً على التاريخ والوقت الحالي
            timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
            backup_filename = f"backup_{timestamp}.db"
            backup_path = os.path.join(backup_folder, backup_filename)

            # نسخ ملف قاعدة البيانات
            shutil.copy2(current_db_path, backup_path)

            # الحصول على حجم الملف
            file_size = os.path.getsize(backup_path)
            if file_size < 1024:
                size_str = f"{file_size} بايت"
            elif file_size < 1024 * 1024:
                size_str = f"{file_size / 1024:.1f} كيلوبايت"
            else:
                size_str = f"{file_size / (1024 * 1024):.1f} ميجابايت"

            # عرض رسالة نجاح مع المعلومات الفعلية
            QMessageBox.information(
                self,
                "تم إنشاء النسخة الاحتياطية بنجاح",
                f"تم إنشاء نسخة احتياطية بنجاح!\n\n"
                f"📁 اسم الملف: {backup_filename}\n"
                f"📍 المسار الكامل: {backup_path}\n"
                f"📊 حجم الملف: {size_str}\n"
                f"📅 تاريخ الإنشاء: {datetime.now().strftime('%Y/%m/%d %H:%M:%S')}"
            )

            # تحديث قائمة النسخ الاحتياطية
            self.refresh_backups_list()

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ في إنشاء النسخة الاحتياطية",
                f"حدث خطأ أثناء إنشاء النسخة الاحتياطية:\n\n{str(e)}"
            )

    def restore_backup(self):
        """استعادة نسخة احتياطية من ملف"""
        file_name, _ = QFileDialog.getOpenFileName(
            self,
            "اختر ملف النسخة الاحتياطية",
            "",
            "قواعد بيانات (*.db *.sqlite);;كل الملفات (*.*)"
        )
        if file_name:
            # التأكيد قبل الاستعادة
            reply = QMessageBox.warning(
                self,
                "تأكيد الاستعادة",
                f"هل أنت متأكد من استعادة النسخة الاحتياطية من الملف:\n\n"
                f"📁 {file_name}\n\n"
                f"⚠️ تحذير: ستتم استبدال البيانات الحالية بالكامل!\n"
                f"هذا الإجراء لا يمكن التراجع عنه.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # تنفيذ عملية الاستعادة الفعلية
                from controllers.database_controller import DatabaseController

                try:
                    success, message = DatabaseController.restore_backup(file_name)

                    if success:
                        QMessageBox.information(
                            self,
                            "تمت الاستعادة",
                            f"تم استعادة النسخة الاحتياطية بنجاح!\n\n"
                            f"📁 الملف: {file_name.split('/')[-1]}\n"
                            f"✅ تم استبدال قاعدة البيانات الحالية"
                        )

                        # تحديث قائمة النسخ الاحتياطية
                        self.refresh_backups_list()

                        # تحديث جميع الصفحات في التطبيق
                        self.refresh_all_application_pages()

                    else:
                        QMessageBox.critical(
                            self,
                            "خطأ في الاستعادة",
                            f"فشل في استعادة النسخة الاحتياطية:\n\n{message}"
                        )

                except Exception as e:
                    QMessageBox.critical(
                        self,
                        "خطأ في الاستعادة",
                        f"حدث خطأ أثناء استعادة النسخة الاحتياطية:\n\n{str(e)}"
                    )

    def refresh_backups_list(self):
        """تحديث قائمة النسخ الاحتياطية"""
        try:
            import os
            from datetime import datetime

            # الحصول على مسار مجلد النسخ الاحتياطي
            backup_folder = self.backup_folder_input.text() or "backups"

            # مسح الجدول الحالي
            self.backups_table.setRowCount(0)

            # التحقق من وجود المجلد
            if not os.path.exists(backup_folder):
                # عرض رسالة في شريط الحالة
                window = self.window()
                status_bar = window.statusBar if hasattr(window, 'statusBar') else None
                if status_bar:
                    status_bar.showMessage("مجلد النسخ الاحتياطي غير موجود", 3000)
                return

            # البحث عن ملفات النسخ الاحتياطية
            backup_files = []
            for filename in os.listdir(backup_folder):
                if filename.endswith('.db') or filename.endswith('.sqlite'):
                    file_path = os.path.join(backup_folder, filename)
                    if os.path.isfile(file_path):
                        # الحصول على معلومات الملف
                        stat = os.stat(file_path)

                        # تنسيق التاريخ
                        modified_time = datetime.fromtimestamp(stat.st_mtime)
                        date_str = modified_time.strftime("%Y/%m/%d %H:%M")

                        # تنسيق حجم الملف
                        file_size = stat.st_size
                        if file_size < 1024:
                            size_str = f"{file_size} بايت"
                        elif file_size < 1024 * 1024:
                            size_str = f"{file_size / 1024:.1f} كيلوبايت"
                        else:
                            size_str = f"{file_size / (1024 * 1024):.1f} ميجابايت"

                        backup_files.append({
                            "name": filename,
                            "date": date_str,
                            "size": size_str,
                            "timestamp": stat.st_mtime
                        })

            # ترتيب الملفات حسب التاريخ (الأحدث أولاً)
            backup_files.sort(key=lambda x: x["timestamp"], reverse=True)

            # إضافة الملفات إلى الجدول
            for backup in backup_files:
                row = self.backups_table.rowCount()
                self.backups_table.insertRow(row)
                self.backups_table.setItem(row, 0, QTableWidgetItem(backup["name"]))
                self.backups_table.setItem(row, 1, QTableWidgetItem(backup["date"]))
                self.backups_table.setItem(row, 2, QTableWidgetItem(backup["size"]))

            # عرض رسالة في شريط الحالة
            window = self.window()
            status_bar = window.statusBar if hasattr(window, 'statusBar') else None
            if status_bar:
                count = len(backup_files)
                status_bar.showMessage(f"تم العثور على {count} نسخة احتياطية", 2000)

        except Exception as e:
            QMessageBox.warning(
                self,
                "خطأ في تحديث القائمة",
                f"حدث خطأ أثناء تحديث قائمة النسخ الاحتياطية:\n\n{str(e)}"
            )

    def setup_auto_backup(self):
        """إعداد النسخ الاحتياطي التلقائي"""
        try:
            # إيقاف المؤقت الحالي
            self.backup_timer.stop()

            # التحقق من تفعيل النسخ الاحتياطي التلقائي
            if not hasattr(self, 'auto_backup_enabled') or not self.auto_backup_enabled.isChecked():
                print("النسخ الاحتياطي التلقائي معطل")
                return

            # الحصول على تكرار النسخ الاحتياطي
            frequency = self.backup_frequency.currentText() if hasattr(self, 'backup_frequency') else "أسبوعياً"

            # تحويل التكرار إلى ميلي ثانية
            intervals = {
                "يومياً": 24 * 60 * 60 * 1000,      # 24 ساعة
                "أسبوعياً": 7 * 24 * 60 * 60 * 1000,  # 7 أيام
                "شهرياً": 30 * 24 * 60 * 60 * 1000    # 30 يوم
            }

            interval = intervals.get(frequency, intervals["أسبوعياً"])

            # بدء المؤقت
            self.backup_timer.start(interval)

            print(f"تم تفعيل النسخ الاحتياطي التلقائي: {frequency} ({interval/1000/60/60:.1f} ساعة)")

        except Exception as e:
            print(f"خطأ في إعداد النسخ الاحتياطي التلقائي: {str(e)}")

    def perform_auto_backup(self):
        """تنفيذ النسخ الاحتياطي التلقائي"""
        try:
            print("بدء النسخ الاحتياطي التلقائي...")

            import os
            import shutil
            from datetime import datetime

            # الحصول على مسار قاعدة البيانات الحالية
            current_db_path = self.db_path_input.text() if hasattr(self, 'db_path_input') else "database/store.db"

            # التحقق من وجود ملف قاعدة البيانات
            if not os.path.exists(current_db_path):
                print(f"لا يمكن العثور على ملف قاعدة البيانات: {current_db_path}")
                return

            # الحصول على مسار مجلد النسخ الاحتياطي
            backup_folder = self.backup_folder_input.text() if hasattr(self, 'backup_folder_input') else "backups"

            # إنشاء مجلد النسخ الاحتياطي إذا لم يكن موجوداً
            if not os.path.exists(backup_folder):
                os.makedirs(backup_folder)

            # إنشاء اسم الملف بناءً على التاريخ والوقت الحالي
            timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
            backup_filename = f"auto_backup_{timestamp}.db"
            backup_path = os.path.join(backup_folder, backup_filename)

            # نسخ ملف قاعدة البيانات
            shutil.copy2(current_db_path, backup_path)

            print(f"تم إنشاء نسخة احتياطية تلقائية: {backup_path}")

            # تنظيف النسخ القديمة
            self.cleanup_old_backups()

            # تحديث قائمة النسخ الاحتياطية إذا كان التاب مفتوح
            if hasattr(self, 'refresh_backups_list'):
                self.refresh_backups_list()

        except Exception as e:
            print(f"خطأ في النسخ الاحتياطي التلقائي: {str(e)}")

    def cleanup_old_backups(self):
        """تنظيف النسخ الاحتياطية القديمة"""
        try:
            import os
            from datetime import datetime

            # الحصول على مسار مجلد النسخ الاحتياطي
            backup_folder = self.backup_folder_input.text() if hasattr(self, 'backup_folder_input') else "backups"

            # الحصول على عدد النسخ المحتفظ بها
            max_backups = self.max_backups.value() if hasattr(self, 'max_backups') else 5

            if not os.path.exists(backup_folder):
                return

            # البحث عن ملفات النسخ الاحتياطية
            backup_files = []
            for filename in os.listdir(backup_folder):
                if filename.endswith('.db') or filename.endswith('.sqlite'):
                    file_path = os.path.join(backup_folder, filename)
                    if os.path.isfile(file_path):
                        stat = os.stat(file_path)
                        backup_files.append({
                            "path": file_path,
                            "timestamp": stat.st_mtime
                        })

            # ترتيب الملفات حسب التاريخ (الأقدم أولاً)
            backup_files.sort(key=lambda x: x["timestamp"])

            # حذف الملفات الزائدة
            while len(backup_files) > max_backups:
                old_file = backup_files.pop(0)
                try:
                    os.remove(old_file["path"])
                    print(f"تم حذف النسخة الاحتياطية القديمة: {old_file['path']}")
                except Exception as e:
                    print(f"خطأ في حذف النسخة الاحتياطية القديمة: {str(e)}")

        except Exception as e:
            print(f"خطأ في تنظيف النسخ الاحتياطية القديمة: {str(e)}")

    def show_backup_context_menu(self, position):
        """عرض قائمة السياق لجدول النسخ الاحتياطية"""
        # التحقق من وجود صف محدد
        item = self.backups_table.itemAt(position)
        if item is None:
            return

        # الحصول على الصف المحدد
        row = item.row()
        backup_name = self.backups_table.item(row, 0).text()

        # إنشاء قائمة السياق
        context_menu = QMenu(self)
        context_menu.setLayoutDirection(Qt.RightToLeft)

        # إضافة الإجراءات
        restore_action = context_menu.addAction("🔄  استعادة النسخة الاحتياطية")
        restore_action.setToolTip(f"استعادة النسخة الاحتياطية: {backup_name}")
        restore_action.triggered.connect(lambda: self.restore_selected_backup(row))

        context_menu.addSeparator()

        delete_action = context_menu.addAction("🗑️  حذف النسخة الاحتياطية")
        delete_action.setToolTip(f"حذف النسخة الاحتياطية: {backup_name}")
        delete_action.triggered.connect(lambda: self.delete_selected_backup(row))

        context_menu.addSeparator()

        info_action = context_menu.addAction("ℹ️  معلومات النسخة الاحتياطية")
        info_action.setToolTip(f"عرض معلومات النسخة الاحتياطية: {backup_name}")
        info_action.triggered.connect(lambda: self.show_backup_info(row))

        # تطبيق الأنماط على قائمة السياق
        context_menu.setStyleSheet("""
            QMenu {
                background-color: white;
                border: 1px solid #ddd;
                border-radius: 6px;
                padding: 5px;
                font-size: 12px;
            }
            QMenu::item {
                padding: 8px 20px;
                border-radius: 4px;
                margin: 2px;
            }
            QMenu::item:selected {
                background-color: #e3f2fd;
                color: #1976d2;
            }
            QMenu::separator {
                height: 1px;
                background-color: #eee;
                margin: 5px 10px;
            }
        """)

        # عرض قائمة السياق
        context_menu.exec_(self.backups_table.mapToGlobal(position))

    def restore_selected_backup(self, row):
        """استعادة النسخة الاحتياطية المحددة"""
        backup_name = self.backups_table.item(row, 0).text()
        backup_date = self.backups_table.item(row, 1).text()

        # الحصول على مسار مجلد النسخ الاحتياطية
        backup_folder = self.backup_folder_input.text() if hasattr(self, 'backup_folder_input') else "backups"
        backup_path = os.path.join(backup_folder, backup_name)

        # التأكيد قبل الاستعادة
        reply = QMessageBox.warning(
            self,
            "تأكيد الاستعادة",
            f"هل أنت متأكد من استعادة النسخة الاحتياطية:\n\n"
            f"📁 الملف: {backup_name}\n"
            f"📅 التاريخ: {backup_date}\n\n"
            f"⚠️ تحذير: ستتم استبدال البيانات الحالية بالكامل!",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # تنفيذ عملية الاستعادة الفعلية
            from controllers.database_controller import DatabaseController

            try:
                success, message = DatabaseController.restore_backup(backup_path)

                if success:
                    QMessageBox.information(
                        self,
                        "تمت الاستعادة",
                        f"تم استعادة النسخة الاحتياطية '{backup_name}' بنجاح!"
                    )

                    # تحديث قائمة النسخ الاحتياطية
                    self.refresh_backups_list()

                    # تحديث جميع الصفحات في التطبيق
                    self.refresh_all_application_pages()

                else:
                    QMessageBox.critical(
                        self,
                        "خطأ في الاستعادة",
                        f"فشل في استعادة النسخة الاحتياطية:\n\n{message}"
                    )

            except Exception as e:
                QMessageBox.critical(
                    self,
                    "خطأ في الاستعادة",
                    f"حدث خطأ أثناء استعادة النسخة الاحتياطية:\n\n{str(e)}"
                )

    def refresh_all_application_pages(self):
        """تحديث جميع الصفحات في التطبيق بعد استعادة قاعدة البيانات"""
        try:
            # الحصول على النافذة الرئيسية
            main_window = self.window()

            # استخدام دالة refresh_all_pages في النافذة الرئيسية
            if hasattr(main_window, 'refresh_all_pages') and callable(main_window.refresh_all_pages):
                main_window.refresh_all_pages()
            else:
                # طريقة بديلة إذا لم تكن الدالة متوفرة
                if hasattr(main_window, 'content_widget'):
                    for i in range(main_window.content_widget.count()):
                        widget = main_window.content_widget.widget(i)
                        if hasattr(widget, 'refresh_page') and callable(widget.refresh_page):
                            widget.refresh_page()
                            print(f"تم تحديث الصفحة: {widget.__class__.__name__}")

        except Exception as e:
            print(f"خطأ في تحديث الصفحات: {str(e)}")

            # محاولة بديلة في حالة فشل الطريقة الأولى
            try:
                main_window = self.window()
                if hasattr(main_window, 'content_widget'):
                    for i in range(main_window.content_widget.count()):
                        widget = main_window.content_widget.widget(i)
                        if hasattr(widget, 'refresh_page') and callable(widget.refresh_page):
                            widget.refresh_page()
            except Exception as fallback_error:
                print(f"فشل في الطريقة البديلة: {str(fallback_error)}")
                QMessageBox.warning(
                    self,
                    "تحذير",
                    "تم استعادة قاعدة البيانات بنجاح\nلكن يرجى إعادة تشغيل التطبيق لضمان تحديث جميع البيانات."
                )

    def delete_selected_backup(self, row):
        """حذف النسخة الاحتياطية المحددة"""
        backup_name = self.backups_table.item(row, 0).text()
        backup_date = self.backups_table.item(row, 1).text()

        # التأكيد قبل الحذف
        reply = QMessageBox.warning(
            self,
            "تأكيد الحذف",
            f"هل أنت متأكد من حذف النسخة الاحتياطية:\n\n"
            f"📁 الملف: {backup_name}\n"
            f"📅 التاريخ: {backup_date}\n\n"
            f"⚠️ تحذير: هذا الإجراء لا يمكن التراجع عنه!",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # حذف الصف من الجدول
            self.backups_table.removeRow(row)

            # هنا يمكن إضافة كود لحذف الملف الفعلي
            QMessageBox.information(
                self,
                "تم الحذف",
                f"تم حذف النسخة الاحتياطية '{backup_name}' بنجاح!"
            )

    def show_backup_info(self, row):
        """عرض معلومات النسخة الاحتياطية"""
        backup_name = self.backups_table.item(row, 0).text()
        backup_date = self.backups_table.item(row, 1).text()
        backup_size = self.backups_table.item(row, 2).text()

        # إنشاء نافذة معلومات
        info_dialog = QDialog(self)
        info_dialog.setWindowTitle("معلومات النسخة الاحتياطية")
        info_dialog.setFixedSize(400, 300)
        info_dialog.setLayoutDirection(Qt.RightToLeft)

        layout = QVBoxLayout(info_dialog)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # عنوان النافذة
        title_label = QLabel("📋 معلومات النسخة الاحتياطية")
        title_label.setFont(QFont("Arial", 14, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # معلومات النسخة الاحتياطية
        info_text = f"""
        <div style="font-size: 12px; line-height: 1.6;">
            <p><b>📁 اسم الملف:</b><br>{backup_name}</p>
            <p><b>📅 تاريخ الإنشاء:</b><br>{backup_date}</p>
            <p><b>📊 حجم الملف:</b><br>{backup_size}</p>
            <p><b>📍 المسار:</b><br>backups/{backup_name}</p>
            <p><b>🔧 نوع الملف:</b><br>قاعدة بيانات SQLite</p>
        </div>
        """

        info_label = QLabel(info_text)
        info_label.setWordWrap(True)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 6px;
                padding: 15px;
            }
        """)
        layout.addWidget(info_label)

        # زر الإغلاق
        close_btn = QPushButton("إغلاق")
        close_btn.setObjectName("secondary_button")
        close_btn.clicked.connect(info_dialog.accept)

        button_layout = QHBoxLayout()
        button_layout.addStretch()
        button_layout.addWidget(close_btn)
        layout.addLayout(button_layout)

        # تطبيق الأنماط
        info_dialog.setStyleSheet(AppStyles.get_all_view_styles())

        # عرض النافذة
        info_dialog.exec_()

    def upload_logo(self):
        """تحميل شعار للشركة"""
        file_name, _ = QFileDialog.getOpenFileName(
            self,
            "اختر شعار الشركة",
            "",
            "صور (*.png *.jpg *.jpeg *.bmp *.gif)"
        )
        if file_name:
            self.company_logo_label.setText(file_name.split("/")[-1])
            # هنا يمكن حفظ الشعار أو معالجته
            QMessageBox.information(
                self,
                "تم بنجاح",
                "تم تحميل الشعار بنجاح"
            )

    def load_settings(self):
        """تحميل الإعدادات المحفوظة"""
        # إعدادات الفاتورة
        self.company_name.setText(self.settings.value("company_name", DEFAULT_SETTINGS["company_name"]))
        self.company_phone.setText(self.settings.value("company_phone", DEFAULT_SETTINGS["company_phone"]))

        self.company_address.setText(self.settings.value("company_address", DEFAULT_SETTINGS["company_address"]))
        self.invoice_notes.setText(self.settings.value("invoice_notes", DEFAULT_SETTINGS["invoice_notes"]))

        # إعدادات عرض الشعار على الفاتورة
        show_logo = self.settings.value("show_logo_on_invoice", DEFAULT_SETTINGS["show_logo_on_invoice"])
        if isinstance(show_logo, str):
            show_logo = show_logo.lower() == 'true'
        self.show_logo_on_invoice.setChecked(bool(show_logo))

        # إعدادات عرض معاينة الفاتورة
        show_preview = self.settings.value("show_invoice_preview_after_sale", DEFAULT_SETTINGS["show_invoice_preview_after_sale"])
        if isinstance(show_preview, str):
            show_preview = show_preview.lower() == 'true'
        self.show_invoice_preview_after_sale.setChecked(bool(show_preview))

        # إعدادات قاعدة البيانات SQLite
        self.db_path_input.setText(self.settings.value("db_path", "database.db"))

    def save_settings(self):
        """حفظ إعدادات التطبيق"""
        # جمع إعدادات الفاتورة
        invoice_settings = {
            "company_name": self.company_name.text(),
            "company_phone": self.company_phone.text(),

            "company_address": self.company_address.text(),
            "invoice_notes": self.invoice_notes.text(),
            "show_logo_on_invoice": self.show_logo_on_invoice.isChecked(),
            "show_invoice_preview_after_sale": self.show_invoice_preview_after_sale.isChecked()
        }

        # جمع إعدادات قاعدة البيانات SQLite
        db_settings = {
            "db_type": "sqlite",
            "db_path": self.db_path_input.text()
        }

        # التحقق من صحة مسار قاعدة البيانات SQLite
        if not self.db_path_input.text():
            QMessageBox.warning(
                self,
                "خطأ في الإعدادات",
                "يرجى تحديد مسار ملف قاعدة البيانات SQLite.",
                QMessageBox.Ok
            )
            return

        # حفظ إعدادات الفاتورة
        for key, value in invoice_settings.items():
            self.settings.setValue(key, value)

        # حفظ إعدادات قاعدة البيانات باستخدام وحدة التحكم
        success, message = DatabaseController.save_settings(db_settings)

        if success:
            QMessageBox.information(
                self,
                "تم حفظ الإعدادات",
                "تم حفظ الإعدادات بنجاح.\nقد تحتاج إلى إعادة تشغيل التطبيق لتطبيق جميع التغييرات.",
                QMessageBox.Ok
            )

            # إعادة تحميل الإعدادات في واجهة المستخدم
            self.load_settings()
        else:
            QMessageBox.critical(
                self,
                "خطأ في الحفظ",
                f"حدث خطأ أثناء حفظ إعدادات قاعدة البيانات: {message}",
                QMessageBox.Ok
            )

    def apply_styles(self):
        """تطبيق الأنماط على العناصر"""
        # استخدام التنسيقات من ملف الستايلات بدلاً من التعريف المحلي
        self.setStyleSheet(AppStyles.get_all_view_styles() + """
            QGroupBox {
                font-weight: bold;
                border: 1px solid #cbd5e1;
                border-radius: 6px;
                margin-top: 12px;
                padding-top: 10px;
            }

            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
            }

            QCheckBox {
                spacing: 8px;
            }

            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
        """)

    def preview_invoice_design(self):
        """معاينة تصميم الفاتورة باستخدام المصمم المشترك"""
        from utils.invoice_designer import InvoiceDesigner

        try:
            # إنشاء نافذة معاينة الفاتورة
            dialog = QDialog(self)
            dialog.setWindowTitle("معاينة تصميم الفاتورة - XPrinter")
            dialog.setMinimumSize(380, 550)  # تعديل حجم النافذة ليناسب جدول الفاتورة
            dialog.setLayoutDirection(Qt.RightToLeft)

            # إنشاء التخطيط الرئيسي
            layout = QVBoxLayout(dialog)
            layout.setContentsMargins(20, 20, 20, 20)
            layout.setSpacing(15)

            # إنشاء عنوان الصفحة
            title_label = QLabel("معاينة تصميم الفاتورة")
            title_label.setObjectName("dialog_title")
            title_label.setAlignment(Qt.AlignCenter)
            title_label.setFont(QFont("Arial", 18, QFont.Bold))
            layout.addWidget(title_label)

            # إضافة وصف
            description_label = QLabel("هذه معاينة لتصميم الفاتورة بناءً على الإعدادات الحالية")
            description_label.setAlignment(Qt.AlignCenter)
            description_label.setObjectName("hint_label")
            layout.addWidget(description_label)

            # إنشاء منطقة قابلة للتمرير لمعاينة الفاتورة
            scroll_area = QScrollArea()
            scroll_area.setWidgetResizable(True)
            scroll_area.setFrameShape(QFrame.NoFrame)

            # إنشاء widget الفاتورة باستخدام المصمم المشترك
            invoice_widget = InvoiceDesigner.create_invoice_widget(is_preview=True)

            # تعيين محتوى الفاتورة للمنطقة القابلة للتمرير
            scroll_area.setWidget(invoice_widget)
            layout.addWidget(scroll_area)

            # أزرار الإجراءات
            buttons_layout = QHBoxLayout()

            # زر الطباعة
            print_btn = QPushButton("🖨️  طباعة")
            print_btn.setObjectName("action_button")
            print_btn.setFixedSize(120, 38)
            print_btn.setCursor(Qt.PointingHandCursor)
            print_btn.clicked.connect(lambda: self.print_invoice_preview(invoice_widget))

            # زر تخصيص التصميم
            customize_btn = QPushButton("✏️  تخصيص التصميم")
            customize_btn.setObjectName("secondary_button")
            customize_btn.setFixedSize(150, 38)
            customize_btn.setCursor(Qt.PointingHandCursor)
            customize_btn.clicked.connect(self.customize_invoice_design)

            # زر الإغلاق
            close_btn = QPushButton("إغلاق")
            close_btn.setObjectName("secondary_button")
            close_btn.setFixedSize(120, 38)
            close_btn.setCursor(Qt.PointingHandCursor)
            close_btn.clicked.connect(dialog.reject)

            buttons_layout.addWidget(print_btn)
            buttons_layout.addWidget(customize_btn)
            buttons_layout.addStretch()
            buttons_layout.addWidget(close_btn)

            layout.addLayout(buttons_layout)

            # تطبيق الأنماط
            dialog.setStyleSheet(AppStyles.get_all_view_styles())

            # عرض النافذة
            dialog.exec_()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء معاينة تصميم الفاتورة: {str(e)}")

    def print_invoice_preview(self, invoice_widget):
        """طباعة معاينة الفاتورة على طابعة XPrinter الحرارية"""
        try:
            # إعداد الطابعة للورق الصغير (طابعة الفواتير XPrinter)
            printer = QPrinter(QPrinter.HighResolution)

            # تعيين حجم الورق للطابعات XPrinter الحرارية
            # معظم طابعات XPrinter تستخدم عرض 58 مم أو 80 مم
            # نستخدم هنا خيارين للمستخدم

            # إنشاء مربع حوار لاختيار حجم الورق
            paper_size_dialog = QDialog(self)
            paper_size_dialog.setWindowTitle("اختيار حجم الورق")
            paper_size_dialog.setFixedSize(300, 150)
            paper_size_dialog.setLayoutDirection(Qt.RightToLeft)

            dialog_layout = QVBoxLayout(paper_size_dialog)

            # إضافة عنوان
            title_label = QLabel("اختر حجم الورق المناسب لطابعة XPrinter:")
            title_label.setAlignment(Qt.AlignCenter)
            dialog_layout.addWidget(title_label)

            # إضافة خيارات حجم الورق
            size_58mm = QPushButton("58 مم (طابعة صغيرة)")
            size_58mm.setObjectName("action_button")

            size_80mm = QPushButton("80 مم (طابعة متوسطة)")
            size_80mm.setObjectName("secondary_button")

            dialog_layout.addWidget(size_58mm)
            dialog_layout.addWidget(size_80mm)

            # تعيين حجم الورق بناءً على اختيار المستخدم
            paper_width = 80  # القيمة الافتراضية

            # ربط الأزرار بالوظائف
            def set_58mm():
                nonlocal paper_width
                paper_width = 58
                paper_size_dialog.accept()

            def set_80mm():
                nonlocal paper_width
                paper_width = 80
                paper_size_dialog.accept()

            size_58mm.clicked.connect(set_58mm)
            size_80mm.clicked.connect(set_80mm)

            # عرض مربع الحوار
            paper_size_dialog.exec_()

            # تعيين حجم الورق المختار
            printer.setPageSize(QPrinter.Custom)
            # استخدام طول متغير بناءً على محتوى الفاتورة (يمكن للطابعة الحرارية قطع الورق تلقائياً)
            # طول الورق يعتمد على محتوى الفاتورة، لكن نستخدم قيمة تقريبية هنا
            estimated_height = 200  # تقدير تقريبي للطول بالمليمتر
            printer.setPaperSize(QSizeF(paper_width, estimated_height), QPrinter.Millimeter)

            # تعيين الهوامش لتكون صغيرة جداً
            printer.setPageMargins(1, 1, 1, 1, QPrinter.Millimeter)

            # تعيين اسم الطابعة الافتراضي لطابعات XPrinter
            # في التطبيق الحقيقي، يمكن البحث عن طابعات XPrinter المتصلة
            printer.setPrinterName("XPrinter")

            # فتح مربع حوار الطباعة
            dialog = QPrintDialog(printer, self)

            if dialog.exec_() == QPrintDialog.Accepted:
                # استخدام المساعد الحراري للطباعة الآمنة
                from utils.thermal_printer_helper import ThermalPrinterHelper

                success = ThermalPrinterHelper.safe_print_widget(invoice_widget, printer, self)

                if success:
                    QMessageBox.information(
                        self,
                        "طباعة الفاتورة",
                        f"تم إرسال الفاتورة إلى الطابعة الحرارية بعرض {paper_width} مم بنجاح!"
                    )

        except Exception as e:
            error_msg = f"حدث خطأ أثناء محاولة طباعة الفاتورة:\n{str(e)}\n\nتأكد من:\n"
            error_msg += "• تشغيل الطابعة الحرارية\n"
            error_msg += "• توصيل كابل USB أو الشبكة\n"
            error_msg += "• توفر ورق الطباعة\n"
            error_msg += "• تثبيت تعريف الطابعة الصحيح"

            QMessageBox.critical(self, "خطأ في الطباعة", error_msg)

    def customize_invoice_design(self):
        """فتح نافذة تخصيص تصميم الفاتورة"""
        try:
            QMessageBox.information(
                self,
                "تخصيص التصميم",
                "سيتم إضافة ميزة تخصيص تصميم الفاتورة في تحديث قادم."
            )
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء فتح نافذة تخصيص التصميم: {str(e)}")

    def refresh_page(self):
        """تحديث بيانات الصفحة عند الانتقال إليها أو عند فتح التطبيق"""
        # تحديث قائمة النسخ الاحتياطية
        self.refresh_backups_list()

        # إعادة تحميل الإعدادات
        self.load_settings()

        print("تم تحديث صفحة الإعدادات")

    def add_user(self):
        """إضافة مستخدم جديد"""
        try:
            dialog = QDialog(self)
            dialog.setWindowTitle("إضافة مستخدم جديد")
            dialog.setMinimumWidth(400)

            # تخطيط النموذج
            form_layout = QFormLayout(dialog)

            # حقول المستخدم
            username = QLineEdit()
            username.setObjectName("search_input")
            username.setPlaceholderText("اسم المستخدم")

            full_name = QLineEdit()
            full_name.setObjectName("search_input")
            full_name.setPlaceholderText("الاسم الكامل")

            password = QLineEdit()
            password.setObjectName("search_input")
            password.setPlaceholderText("كلمة المرور")
            password.setEchoMode(QLineEdit.Password)

            confirm_password = QLineEdit()
            confirm_password.setObjectName("search_input")
            confirm_password.setPlaceholderText("تأكيد كلمة المرور")
            confirm_password.setEchoMode(QLineEdit.Password)

            role_combo = RTLComboBox()
            role_combo.addItems(["مدير", "كاشير", "مدير مخزون"])
            role_combo.setObjectName("search_input")

            # إضافة الحقول إلى التخطيط
            form_layout.addRow("اسم المستخدم:", username)
            form_layout.addRow("الاسم الكامل:", full_name)
            form_layout.addRow("كلمة المرور:", password)
            form_layout.addRow("تأكيد كلمة المرور:", confirm_password)
            form_layout.addRow("الصلاحية:", role_combo)

            # أزرار الإجراءات
            button_box = QHBoxLayout()
            save_btn = QPushButton("إضافة المستخدم")
            save_btn.setObjectName("action_button")

            cancel_btn = QPushButton("إلغاء")
            cancel_btn.setObjectName("secondary_button")

            button_box.addWidget(save_btn)
            button_box.addWidget(cancel_btn)

            form_layout.addRow("", button_box)

            # ربط الأزرار بالوظائف
            cancel_btn.clicked.connect(dialog.reject)

            # وظيفة إضافة المستخدم
            def create_user():
                # التحقق من الإدخال
                if not username.text() or not full_name.text() or not password.text() or not confirm_password.text():
                    QMessageBox.warning(dialog, "بيانات غير مكتملة", "يرجى ملء جميع الحقول")
                    return

                # التحقق من تطابق كلمة المرور
                if password.text() != confirm_password.text():
                    QMessageBox.warning(dialog, "كلمات المرور غير متطابقة", "كلمة المرور وتأكيدها غير متطابقين")
                    return

                # إضافة المستخدم إلى قاعدة البيانات
                success, message = UserController.create_user(
                    username.text(),
                    full_name.text(),
                    password.text(),
                    role_combo.currentText()
                )

                if success:
                    # استخدام الرسالة النصية المعادة مباشرة من نموذج المستخدم
                    QMessageBox.information(dialog, "تمت الإضافة", message)
                    self.load_users_data()  # إعادة تحميل بيانات المستخدمين
                    dialog.accept()
                else:
                    QMessageBox.warning(dialog, "خطأ", message)

            save_btn.clicked.connect(create_user)

            # عرض الحوار
            dialog.exec_()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إضافة مستخدم جديد: {str(e)}")

    def edit_user(self):
        """تعديل المستخدم المحدد"""
        try:
            # التحقق من اختيار صف في الجدول
            selected_rows = self.users_table.selectedItems()
            if not selected_rows:
                QMessageBox.warning(self, "تحذير", "الرجاء اختيار مستخدم لتعديله")
                return

            # الحصول على معرف المستخدم المحدد
            selected_row = self.users_table.currentRow()
            user_id = self.users_table.item(selected_row, 0).data(Qt.UserRole)

            # الحصول على معلومات المستخدم
            user_info = UserController.get_user_info(user_id=user_id)
            if not user_info:
                QMessageBox.warning(self, "خطأ", "لم يتم العثور على معلومات المستخدم")
                return

            # إنشاء نافذة الحوار لتعديل المستخدم
            dialog = QDialog(self)
            dialog.setWindowTitle("تعديل المستخدم")
            dialog.setMinimumWidth(400)

            # تخطيط النموذج
            form_layout = QFormLayout(dialog)

            # حقول المستخدم
            username = QLineEdit()
            username.setObjectName("search_input")
            username.setText(user_info['username'])
            if user_info['username'] == 'admin':  # منع تغيير اسم المستخدم للمدير الرئيسي
                username.setEnabled(False)

            full_name = QLineEdit()
            full_name.setObjectName("search_input")
            full_name.setText(user_info['full_name'])

            role_combo = RTLComboBox()
            role_combo.addItems(["مدير", "كاشير", "مدير مخزون"])
            role_combo.setCurrentText(user_info['role'])
            role_combo.setObjectName("search_input")
            if user_info['username'] == 'admin':  # منع تغيير صلاحية المدير الرئيسي
                role_combo.setEnabled(False)

            active_checkbox = QCheckBox("مستخدم نشط")
            active_checkbox.setChecked(user_info['is_active'])

            # إضافة الحقول إلى التخطيط
            form_layout.addRow("اسم المستخدم:", username)
            form_layout.addRow("الاسم الكامل:", full_name)
            form_layout.addRow("الصلاحية:", role_combo)
            form_layout.addRow("", active_checkbox)

            # أزرار الإجراءات
            button_box = QHBoxLayout()
            save_btn = QPushButton("حفظ التغييرات")
            save_btn.setObjectName("action_button")

            reset_password_btn = QPushButton("إعادة تعيين كلمة المرور")
            reset_password_btn.setObjectName("secondary_button")

            cancel_btn = QPushButton("إلغاء")
            cancel_btn.setObjectName("secondary_button")

            button_box.addWidget(save_btn)
            button_box.addWidget(reset_password_btn)
            button_box.addWidget(cancel_btn)

            form_layout.addRow("", button_box)

            # ربط الأزرار بالوظائف
            cancel_btn.clicked.connect(dialog.reject)

            # وظيفة تحديث المستخدم
            def update_user():
                # التحقق من الإدخال
                if not username.text() or not full_name.text():
                    QMessageBox.warning(dialog, "بيانات غير مكتملة", "يرجى ملء جميع الحقول")
                    return

                # تحديث معلومات المستخدم
                success, message = UserController.update_user(
                    user_id,
                    username.text(),
                    full_name.text(),
                    role_combo.currentText(),
                    active_checkbox.isChecked()
                )

                if success:
                    QMessageBox.information(dialog, "تم التحديث", message)
                    self.load_users_data()  # إعادة تحميل بيانات المستخدمين
                    dialog.accept()
                else:
                    QMessageBox.warning(dialog, "خطأ", message)

            save_btn.clicked.connect(update_user)

            # وظيفة إعادة تعيين كلمة المرور
            def reset_password():
                password_dialog = QDialog(dialog)
                password_dialog.setWindowTitle("إعادة تعيين كلمة المرور")
                password_dialog.setMinimumWidth(400)

                password_layout = QFormLayout(password_dialog)

                # حقول كلمة المرور
                new_password = QLineEdit()
                new_password.setObjectName("search_input")
                new_password.setPlaceholderText("كلمة المرور الجديدة")
                new_password.setEchoMode(QLineEdit.Password)

                confirm_new_password = QLineEdit()
                confirm_new_password.setObjectName("search_input")
                confirm_new_password.setPlaceholderText("تأكيد كلمة المرور الجديدة")
                confirm_new_password.setEchoMode(QLineEdit.Password)

                # أزرار الإجراءات
                password_buttons = QHBoxLayout()
                reset_btn = QPushButton("تغيير كلمة المرور")
                reset_btn.setObjectName("action_button")

                cancel_reset_btn = QPushButton("إلغاء")
                cancel_reset_btn.setObjectName("secondary_button")

                password_buttons.addWidget(reset_btn)
                password_buttons.addWidget(cancel_reset_btn)

                # إضافة الحقول والأزرار إلى التخطيط
                password_layout.addRow("كلمة المرور الجديدة:", new_password)
                password_layout.addRow("تأكيد كلمة المرور:", confirm_new_password)
                password_layout.addRow("", password_buttons)

                # ربط الأزرار بالوظائف
                cancel_reset_btn.clicked.connect(password_dialog.reject)

                # وظيفة تغيير كلمة المرور
                def do_reset_password():
                    # التحقق من الإدخال
                    if not new_password.text() or not confirm_new_password.text():
                        QMessageBox.warning(password_dialog, "بيانات غير مكتملة", "يرجى ملء جميع الحقول")
                        return

                    # التحقق من تطابق كلمة المرور
                    if new_password.text() != confirm_new_password.text():
                        QMessageBox.warning(password_dialog, "كلمات المرور غير متطابقة", "كلمة المرور وتأكيدها غير متطابقين")
                        return

                    # تغيير كلمة المرور
                    success, message = UserController.reset_password(user_id, new_password.text())

                    if success:
                        QMessageBox.information(password_dialog, "تم تغيير كلمة المرور", message)
                        password_dialog.accept()
                    else:
                        QMessageBox.warning(password_dialog, "خطأ", message)

                reset_btn.clicked.connect(do_reset_password)

                # عرض الحوار
                password_dialog.exec_()

            reset_password_btn.clicked.connect(reset_password)

            # عرض الحوار
            dialog.exec_()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تعديل المستخدم: {str(e)}")

    def delete_user(self):
        """حذف المستخدم المحدد"""
        try:
            # التحقق من اختيار صف في الجدول
            selected_rows = self.users_table.selectedItems()
            if not selected_rows:
                QMessageBox.warning(self, "تحذير", "الرجاء اختيار مستخدم لحذفه")
                return

            # الحصول على معلومات المستخدم المحدد
            selected_row = self.users_table.currentRow()
            user_id = self.users_table.item(selected_row, 0).data(Qt.UserRole)
            username = self.users_table.item(selected_row, 0).text()

            # منع حذف المستخدم admin
            if username == 'admin':
                QMessageBox.warning(self, "تحذير", "لا يمكن حذف المستخدم الرئيسي (admin)")
                return

            # رسالة تأكيد الحذف
            reply = QMessageBox.question(
                self,
                "تأكيد الحذف",
                f"هل أنت متأكد من حذف المستخدم '{username}'؟",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # حذف المستخدم
                success, message = UserController.delete_user(user_id)

                if success:
                    QMessageBox.information(self, "تم الحذف", message)
                    self.load_users_data()  # إعادة تحميل بيانات المستخدمين
                else:
                    QMessageBox.warning(self, "خطأ", message)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حذف المستخدم: {str(e)}")

    def show_users_context_menu(self, position):
        """عرض قائمة السياق لجدول المستخدمين"""
        try:
            # التحقق من اختيار صف في الجدول
            selected_row = self.users_table.currentRow()
            if selected_row < 0:
                return

            # الحصول على معلومات المستخدم المحدد
            user_id = self.users_table.item(selected_row, 0).data(Qt.UserRole)
            username = self.users_table.item(selected_row, 0).text()

            # إنشاء قائمة السياق
            context_menu = QMenu(self)

            # إضافة عنوان القائمة
            title_action = QAction(f"المستخدم: {username}", self)
            title_action.setEnabled(False)
            title_font = title_action.font()
            title_font.setBold(True)
            title_action.setFont(title_font)
            context_menu.addAction(title_action)

            # إضافة فاصل
            context_menu.addSeparator()

            # إضافة خيارات القائمة
            edit_action = QAction("✏️ تعديل المستخدم", self)
            edit_action.triggered.connect(lambda: self.edit_user())
            context_menu.addAction(edit_action)

            # التحقق من أن المستخدم الحالي هو admin قبل إضافة خيار إدارة الصلاحيات
            try:
                # الحصول على النافذة الرئيسية
                main_window = None
                parent = self.parent()
                while parent:
                    if hasattr(parent, 'current_user'):
                        main_window = parent
                        break
                    parent = parent.parent()

                # التحقق من أن المستخدم الحالي هو admin
                if main_window and main_window.current_user and main_window.current_user.get('username') == 'admin':
                    # إضافة خيار إدارة الصلاحيات (إلا إذا كان المستخدم المحدد هو admin)
                    if username != 'admin':
                        permissions_action = QAction("🔐 إدارة صلاحيات المستخدم", self)
                        permissions_action.triggered.connect(lambda: self.manage_user_permissions(user_id, username))
                        context_menu.addAction(permissions_action)
            except Exception as e:
                print(f"خطأ في التحقق من المستخدم الحالي: {str(e)}")

            # إضافة خيار حذف المستخدم (إلا إذا كان المستخدم هو admin)
            if username != 'admin':
                delete_action = QAction("❌ حذف المستخدم", self)
                delete_action.triggered.connect(lambda: self.delete_user())
                context_menu.addAction(delete_action)

            # عرض القائمة في الموضع المحدد
            context_menu.exec_(self.users_table.mapToGlobal(position))

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء عرض قائمة السياق: {str(e)}")

    def manage_user_permissions(self, user_id, username):
        """إدارة صلاحيات المستخدم المحدد"""
        try:
            # التحقق من أن المستخدم الحالي هو admin
            try:
                # الحصول على النافذة الرئيسية
                main_window = None
                parent = self.parent()
                while parent:
                    if hasattr(parent, 'current_user'):
                        main_window = parent
                        break
                    parent = parent.parent()

                # التحقق من أن المستخدم الحالي هو admin
                if not main_window or not main_window.current_user or main_window.current_user.get('username') != 'admin':
                    QMessageBox.warning(self, "غير مسموح", "فقط المستخدم الرئيسي (admin) يمكنه إدارة صلاحيات المستخدمين")
                    return
            except Exception as e:
                print(f"خطأ في التحقق من المستخدم الحالي: {str(e)}")
                QMessageBox.warning(self, "خطأ", "حدث خطأ أثناء التحقق من صلاحيات المستخدم")
                return

            # التحقق من أن المستخدم المراد تعديل صلاحياته ليس admin
            if username == 'admin':
                QMessageBox.warning(self, "غير مسموح", "لا يمكن تعديل صلاحيات المستخدم الرئيسي (admin)")
                return

            # الحصول على معلومات المستخدم
            user_info = UserController.get_user_info(user_id=user_id)
            if not user_info:
                QMessageBox.warning(self, "خطأ", "لم يتم العثور على معلومات المستخدم")
                return

            # الحصول على صلاحيات المستخدم الحالية
            user_permissions = UserController.get_user_permissions(user_id)
            print(f"تم استرجاع صلاحيات المستخدم: {user_id} - عدد الصلاحيات: {len(user_permissions) if user_permissions else 0}")

            # إنشاء نافذة حوار لإدارة الصلاحيات
            dialog = QDialog(self)
            dialog.setWindowTitle(f"إدارة صلاحيات المستخدم: {username}")
            dialog.setMinimumWidth(500)
            dialog.setMinimumHeight(600)

            # تخطيط النافذة
            layout = QVBoxLayout(dialog)

            # عنوان
            title_label = QLabel(f"تحديد صلاحيات المستخدم: {username}")
            title_label.setObjectName("page_title")
            title_label.setFont(QFont("Arial", 14, QFont.Bold))
            layout.addWidget(title_label)

            # معلومات المستخدم
            info_layout = QHBoxLayout()
            role_label = QLabel(f"الصلاحية: {user_info['role']}")
            role_label.setStyleSheet("color: #3b82f6; font-weight: bold;")
            info_layout.addWidget(role_label)

            status_label = QLabel(f"الحالة: {user_info['status']}")
            status_label.setStyleSheet(f"color: {'#27ae60' if user_info['is_active'] else '#e74c3c'}; font-weight: bold;")
            info_layout.addWidget(status_label)

            layout.addLayout(info_layout)

            # إضافة فاصل
            separator = QFrame()
            separator.setFrameShape(QFrame.HLine)
            separator.setFrameShadow(QFrame.Sunken)
            separator.setObjectName("content_separator")
            layout.addWidget(separator)

            # شرح مختصر
            info_label = QLabel("حدد الصلاحيات التي تريد منحها لهذا المستخدم:")
            info_label.setObjectName("hint_label")
            info_label.setWordWrap(True)
            layout.addWidget(info_label)

            # إنشاء شجرة الصلاحيات
            permissions_tree = QTreeWidget()
            permissions_tree.setHeaderLabels(["الصلاحية", "الحالة"])
            permissions_tree.setAlternatingRowColors(True)
            permissions_tree.setRootIsDecorated(True)
            permissions_tree.setColumnWidth(0, 350)

            # الصلاحيات المتاحة - مجموعة بحسب الوظائف
            available_permissions = {
                "المبيعات": [
                    "عرض المبيعات",
                    "إضافة عملية بيع",
                    "تعديل عملية بيع",
                    "حذف عملية بيع",
                    "طباعة فاتورة مبيعات"
                ],
                "المنتجات": [
                    "عرض المنتجات",
                    "إضافة منتج",
                    "تعديل منتج",
                    "حذف منتج",
                    "إدارة فئات المنتجات",
                    "تعديل أسعار المنتجات"
                ],
                "المخزون": [
                    "عرض المخزون",
                    "إضافة للمخزون",
                    "تعديل المخزون",
                    "جرد المخزون"
                ],
                "الفواتير": [
                    "عرض الفواتير",
                    "إلغاء فاتورة",
                    "طباعة فاتورة",
                    "تعديل فاتورة"
                ],
                "التقارير": [
                    "عرض تقارير المبيعات",
                    "عرض تقارير المخزون",
                    "عرض تقارير الأرباح",
                    "عرض تقارير العملاء",
                    "عرض تقارير الموردين",
                    "عرض تقرير المنتجات الأكثر مبيعاً",
                    "تصدير التقارير"
                ],
                "العملاء": [
                    "عرض العملاء",
                    "إضافة عميل",
                    "تعديل عميل",
                    "حذف عميل",
                    "إدارة ديون العملاء",
                    "عرض تفاصيل العميل"
                ],
                "الموردين": [
                    "عرض الموردين",
                    "إضافة مورد",
                    "تعديل مورد",
                    "حذف مورد",
                    "إدارة مدفوعات الموردين",
                    "عرض تفاصيل المورد"
                ],
                "المصروفات": [
                    "عرض المصروفات",
                    "إضافة مصروف",
                    "تعديل مصروف",
                    "حذف مصروف"
                ],
                "المستخدمين": [
                    "عرض المستخدمين",
                    "إضافة مستخدم",
                    "تعديل مستخدم",
                    "حذف مستخدم",
                    "إدارة صلاحيات المستخدمين"
                ],
                "الإعدادات": [
                    "عرض الإعدادات",
                    "تعديل إعدادات النظام",
                    "إدارة النسخ الاحتياطي",
                    "استعادة النسخ الاحتياطي"
                ]
            }

            # إضافة الصلاحيات إلى الشجرة
            category_items = {}

            for category, permissions in available_permissions.items():
                # إنشاء عنصر الفئة
                category_item = QTreeWidgetItem(permissions_tree, [category])
                # تعيين العلم لجعل العنصر قابل للتحديد فقط (بدون Qt.ItemIsTristate)
                category_item.setFlags(category_item.flags() | Qt.ItemIsUserCheckable)
                category_item.setCheckState(0, Qt.Unchecked)
                category_items[category] = category_item

                # إضافة الصلاحيات الفرعية
                all_checked = True  # للتحقق مما إذا كانت جميع الصلاحيات الفرعية محددة

                for permission in permissions:
                    child = QTreeWidgetItem(category_item, [permission])
                    child.setFlags(child.flags() | Qt.ItemIsUserCheckable)

                    # تحديد حالة الصلاحية بناءً على صلاحيات المستخدم
                    is_checked = permission in user_permissions and user_permissions[permission]
                    child.setCheckState(0, Qt.Checked if is_checked else Qt.Unchecked)

                    # تحديث حالة التحقق من جميع الصلاحيات الفرعية
                    if not is_checked:
                        all_checked = False

                # تعيين حالة عنصر الفئة بناءً على حالة جميع الصلاحيات الفرعية
                if all_checked and len(permissions) > 0:  # تأكد من وجود صلاحيات فرعية
                    category_item.setCheckState(0, Qt.Checked)

            # تحديث حالة عناصر الفئات بناءً على حالة العناصر الفرعية
            def update_parent_item(item):
                if item.childCount() > 0:
                    all_checked = True
                    any_checked = False

                    for i in range(item.childCount()):
                        child = item.child(i)
                        if child.checkState(0) == Qt.Checked:
                            any_checked = True
                        else:
                            all_checked = False

                    if all_checked:
                        item.setCheckState(0, Qt.Checked)
                    elif any_checked:
                        item.setCheckState(0, Qt.PartiallyChecked)
                    else:
                        item.setCheckState(0, Qt.Unchecked)

            # تحديث حالة عناصر الفئات الأولية
            for category_item in category_items.values():
                update_parent_item(category_item)

            # متغير لتتبع ما إذا كان التغيير يأتي من المستخدم أو من البرنامج
            is_updating = [False]

            # ربط حدث تغيير حالة العناصر
            def on_item_changed(item, column):
                # تجاهل التغييرات أثناء التحديث البرمجي
                if is_updating[0]:
                    return

                if column == 0:
                    # تعيين علامة التحديث لمنع التكرار
                    is_updating[0] = True

                    try:
                        # إذا كان العنصر فئة (له عناصر فرعية)
                        if item.childCount() > 0:
                            check_state = item.checkState(0)
                            # تحديث جميع العناصر الفرعية لتطابق حالة الفئة
                            for i in range(item.childCount()):
                                child = item.child(i)
                                child.setCheckState(0, check_state)

                        # إذا كان العنصر فرعي، تحديث حالة الفئة الأب فقط
                        elif item.parent():
                            parent = item.parent()

                            # حساب حالة الفئة الأب بناءً على العناصر الفرعية
                            checked_count = 0
                            total_children = parent.childCount()

                            for i in range(total_children):
                                if parent.child(i).checkState(0) == Qt.Checked:
                                    checked_count += 1

                            # تحديث حالة الفئة الأب
                            if checked_count == 0:
                                parent.setCheckState(0, Qt.Unchecked)
                            elif checked_count == total_children:
                                parent.setCheckState(0, Qt.Checked)
                            else:
                                parent.setCheckState(0, Qt.PartiallyChecked)
                    finally:
                        # إعادة تعيين علامة التحديث
                        is_updating[0] = False

            permissions_tree.itemChanged.connect(on_item_changed)

            layout.addWidget(permissions_tree)

            # أزرار الإجراءات
            buttons_layout = QHBoxLayout()

            save_btn = QPushButton("حفظ الصلاحيات")
            save_btn.setObjectName("action_button")

            cancel_btn = QPushButton("إلغاء")
            cancel_btn.setObjectName("secondary_button")

            buttons_layout.addWidget(save_btn)
            buttons_layout.addWidget(cancel_btn)
            buttons_layout.addStretch()

            layout.addLayout(buttons_layout)

            # ربط الأزرار بالوظائف
            cancel_btn.clicked.connect(dialog.reject)

            # وظيفة حفظ الصلاحيات
            def save_user_permissions():
                # جمع الصلاحيات المحددة
                permissions_data = {}

                # جمع الصلاحيات من جميع الفئات
                for _, category_item in category_items.items():
                    for i in range(category_item.childCount()):
                        child = category_item.child(i)
                        permission_name = child.text(0)
                        # التحقق من حالة العنصر الفرعي
                        is_allowed = child.checkState(0) == Qt.Checked
                        permissions_data[permission_name] = is_allowed

                print(f"جاري حفظ {len(permissions_data)} صلاحية للمستخدم {user_id}")

                # طباعة الصلاحيات للتحقق
                for perm_name, is_allowed in permissions_data.items():
                    print(f"الصلاحية: {perm_name} = {is_allowed}")

                # حفظ الصلاحيات في قاعدة البيانات
                success, message = UserController.save_user_permissions(user_id, permissions_data)

                if success:
                    QMessageBox.information(dialog, "تم الحفظ", message)
                    dialog.accept()
                else:
                    QMessageBox.warning(dialog, "خطأ", message)

            save_btn.clicked.connect(save_user_permissions)

            # عرض الحوار
            dialog.exec_()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إدارة صلاحيات المستخدم: {str(e)}")

    def on_permission_changed(self, row, col, permission_name, category, state):
        """تغيير صلاحيات المستخدم عند تغيير قيمة checkbox"""
        # تمييز أن هناك تغييرات لم يتم حفظها
        role_name = self.permissions_table.horizontalHeaderItem(col).text()

        print(f"تم تغيير صلاحية '{permission_name}' في فئة '{category}' للدور '{role_name}' إلى {state > 0}")

        # تحديث حالة checkbox الفئة بناءً على حالة جميع الصلاحيات الفرعية
        self.update_category_checkbox_state(category, col)

    def on_category_permission_changed(self, category, row, col, state):
        """تغيير جميع صلاحيات الفئة عند تغيير قيمة checkbox الفئة"""
        role_name = self.permissions_table.horizontalHeaderItem(col).text()
        print(f"تم تغيير جميع صلاحيات فئة '{category}' للدور '{role_name}' إلى {state > 0}")

        # تحديث جميع الصلاحيات الفرعية للفئة
        self.update_all_category_permissions(category, col, state > 0)

    def update_category_checkbox_state(self, category, col):
        """تحديث حالة checkbox الفئة بناءً على حالة جميع الصلاحيات الفرعية"""
        # البحث عن صف الفئة
        category_row = -1
        for row in range(self.permissions_table.rowCount()):
            item = self.permissions_table.item(row, 0)
            if item and item.text().strip() == category:
                category_row = row
                break

        if category_row == -1:
            return

        # البحث عن جميع الصلاحيات الفرعية للفئة
        all_checked = True
        any_checked = False

        row = category_row + 1
        while row < self.permissions_table.rowCount():
            item = self.permissions_table.item(row, 0)
            if not item or not item.text().startswith("    "):  # إذا وصلنا إلى فئة جديدة
                break

            # فحص حالة الـ checkbox
            cell_widget = self.permissions_table.cellWidget(row, col)
            if cell_widget:
                checkbox = cell_widget.layout().itemAt(0).widget()
                if checkbox.isChecked():
                    any_checked = True
                else:
                    all_checked = False

            row += 1

        # تحديث حالة checkbox الفئة
        category_cell_widget = self.permissions_table.cellWidget(category_row, col)
        if category_cell_widget:
            category_checkbox = category_cell_widget.layout().itemAt(0).widget()

            # تعيين حالة الـ checkbox بناءً على حالة الصلاحيات الفرعية
            # إذا كانت جميع الصلاحيات الفرعية محددة، يتم تحديد checkbox الفئة
            # إذا كانت بعض الصلاحيات الفرعية محددة، يتم تعيين حالة checkbox الفئة إلى Qt.PartiallyChecked
            # إذا لم تكن أي صلاحية فرعية محددة، يتم إلغاء تحديد checkbox الفئة
            if all_checked:
                category_checkbox.setCheckState(Qt.Checked)
            elif any_checked:
                category_checkbox.setCheckState(Qt.PartiallyChecked)
            else:
                category_checkbox.setCheckState(Qt.Unchecked)

    def update_all_category_permissions(self, category, col, checked):
        """تحديث جميع الصلاحيات الفرعية للفئة"""
        # البحث عن صف الفئة
        category_row = -1
        for row in range(self.permissions_table.rowCount()):
            item = self.permissions_table.item(row, 0)
            if item and item.text().strip() == category:
                category_row = row
                break

        if category_row == -1:
            return

        # تحديث جميع الصلاحيات الفرعية للفئة
        row = category_row + 1
        while row < self.permissions_table.rowCount():
            item = self.permissions_table.item(row, 0)
            if not item or not item.text().startswith("    "):  # إذا وصلنا إلى فئة جديدة
                break

            # تحديث حالة الـ checkbox
            cell_widget = self.permissions_table.cellWidget(row, col)
            if cell_widget:
                checkbox = cell_widget.layout().itemAt(0).widget()
                checkbox.setChecked(checked)

            row += 1

    def save_permissions(self):
        """حفظ تغييرات الصلاحيات في قاعدة البيانات"""
        try:
            # إنشاء قاموس لتخزين الصلاحيات
            permissions = {}

            # استرجاع الصلاحيات من الجدول
            for row in range(self.permissions_table.rowCount()):
                item = self.permissions_table.item(row, 0)
                if not item:
                    continue

                text = item.text().strip()

                # تخطي صفوف الفئات
                if not text.startswith("    "):
                    continue

                # استخراج اسم الصلاحية (إزالة المسافات البادئة)
                permission_name = text.strip()

                for col in range(1, 4):  # الأعمدة 1, 2, 3 (مدير، كاشير، مدير مخزون)
                    role_name = self.permissions_table.horizontalHeaderItem(col).text()

                    # الحصول على حالة الـ checkbox
                    cell_widget = self.permissions_table.cellWidget(row, col)
                    if not cell_widget:
                        continue

                    checkbox = cell_widget.layout().itemAt(0).widget()
                    is_allowed = checkbox.isChecked()

                    # إضافة الصلاحية إلى القاموس
                    if role_name not in permissions:
                        permissions[role_name] = {}

                    permissions[role_name][permission_name] = is_allowed

            # حفظ الصلاحيات في قاعدة البيانات
            success, message = UserController.save_role_permissions(permissions)

            if success:
                QMessageBox.information(self, "تم الحفظ", message)
                # إعادة تحميل الصلاحيات لتحديث الواجهة
                self.load_permissions_data()
            else:
                QMessageBox.warning(self, "خطأ", message)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ الصلاحيات: {str(e)}")