c:\Users\<USER>\Desktop\11\main.pyw:86: DeprecationWarning: sipPyTypeDict() is deprecated, the extension module should use sipPyTypeDictRef() instead
  class MainWindow(QMainWindow):
استخدام الخط المحمل من الملف: Readex Pro
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم إنشاء الجداول بنجاح
تم حفظ التغييرات في قاعدة البيانات
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
تم حفظ التغييرات في قاعدة البيانات
لا توجد بيانات في جدول purchase_invoices، لا حاجة للترحيل.
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
المستخدم الرئيسي موجود بالفعل
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
عرض نافذة تسجيل الدخول...
تم حفظ التغييرات في قاعدة البيانات
نتيجة تسجيل الدخول: 1
بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-27 16:27:23', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-27 16:27:23', 'status': 'نشط'}
جاري إنشاء النافذة الرئيسية...
بدء إنشاء النافذة الرئيسية...
تم استدعاء منشئ الفئة الأب
تم تخزين بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-27 16:27:23', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-27 16:27:23', 'status': 'نشط'}
جاري تعيين الصلاحيات...
جاري إنشاء StackedWidget...
تم إنشاء StackedWidget
جاري إنشاء زر وصفحة: المبيعات
تم إنشاء زر: المبيعات
جاري إنشاء صفحة: المبيعات
خطأ في معالجة المنتجات المفضلة: 'SalesView' object has no attribute 'product_buttons'
تم إنشاء صفحة: المبيعات
تم إضافة صفحة: المبيعات إلى StackedWidget
جاري إنشاء زر وصفحة: المنتجات
تم إنشاء زر: المنتجات
جاري إنشاء صفحة: المنتجات
تم إنشاء صفحة: المنتجات
تم إضافة صفحة: المنتجات إلى StackedWidget
جاري إنشاء زر وصفحة: الفواتير
تم إنشاء زر: الفواتير
جاري إنشاء صفحة: الفواتير
فلتر تاريخ البدء: 2025/05/28
فلتر تاريخ الانتهاء: 2025/05/28 (قبل 2025/05/29)
استعلام البحث: 
            SELECT i.id, i.reference_number, i.date, i.subtotal, i.tax, i.discount, 
                   i.total, i.paid_amount, i.remaining_amount, i.payment_method, i.status, 
                   i.notes, c.name as customer_name, c.id as customer_id
            FROM invoices i
            LEFT JOIN customers c ON i.customer_id = c.id
            WHERE 1=1
         AND (i.date >= ? OR i.date LIKE ?) AND i.date < ?
معلمات البحث: ['2025/05/28', '2025/05/28%', '2025/05/29']
تم العثور على 0 فاتورة
تم إنشاء صفحة: الفواتير
تم إضافة صفحة: الفواتير إلى StackedWidget
جاري إنشاء زر وصفحة: التقارير
تم إنشاء زر: التقارير
جاري إنشاء صفحة: التقارير
Invoices after initial filtering (status, customer): 0
تم إنشاء صفحة: التقارير
تم إضافة صفحة: التقارير إلى StackedWidget
جاري إنشاء زر وصفحة: العملاء
تم إنشاء زر: العملاء
جاري إنشاء صفحة: العملاء
تنسيق التاريخ: 2025/05/25 14:10:01
تم إنشاء صفحة: العملاء
تم إضافة صفحة: العملاء إلى StackedWidget
جاري إنشاء زر وصفحة: الموردين
تم إنشاء زر: الموردين
جاري إنشاء صفحة: الموردين
تم استرجاع 2 مورد من قاعدة البيانات
أول مورد: {'id': 10, 'name': 'احمد ربيع', 'phone': '', 'email': '', 'address': '', 'notes': '', 'contact_person': None, 'total_purchases': 5871.5, 'last_purchase_date': '2025/05/14 10:17:48', 'purchase_count': 1, 'total_debt': 2000.0}
تاريخ آخر عملية شراء للمورد احمد ربيع: 2025/05/14 10:17:48
تنسيق التاريخ: 2025/05/14 10:17:48
تاريخ آخر عملية شراء للمورد عمرو ابو رحمة: 2025/05/14 10:28:43
تنسيق التاريخ: 2025/05/14 10:28:43
تم إنشاء صفحة: الموردين
تم إضافة صفحة: الموردين إلى StackedWidget
جاري إنشاء زر وصفحة: المصروفات
تم إنشاء زر: المصروفات
جاري إنشاء صفحة: المصروفات
تم إنشاء صفحة: المصروفات
تم إضافة صفحة: المصروفات إلى StackedWidget
جاري إنشاء زر وصفحة: الإعدادات
تم إنشاء زر: الإعدادات
جاري إنشاء صفحة: الإعدادات
تم تحميل 2 مستخدم من قاعدة البيانات
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم إنشاء صفحة: الإعدادات
تم إضافة صفحة: الإعدادات إلى StackedWidget
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: جاري تحميل العملاء...
تم العثور على 0 عميل
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: عميل نقدي
تم تحديث صفحة المبيعات
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم قطع الاتصال بقاعدة البيانات
جاري تحديث معلومات المستخدم...
تم إنشاء النافذة الرئيسية بنجاح
تم إنشاء النافذة الرئيسية بنجاح
تم عرض النافذة الرئيسية
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم تحديث صفحة المنتجات
تم قطع الاتصال بقاعدة البيانات
فلتر تاريخ البدء: 2025/05/28
فلتر تاريخ الانتهاء: 2025/05/28 (قبل 2025/05/29)
استعلام البحث: 
            SELECT i.id, i.reference_number, i.date, i.subtotal, i.tax, i.discount, 
                   i.total, i.paid_amount, i.remaining_amount, i.payment_method, i.status, 
                   i.notes, c.name as customer_name, c.id as customer_id
            FROM invoices i
            LEFT JOIN customers c ON i.customer_id = c.id
            WHERE 1=1
         AND (i.date >= ? OR i.date LIKE ?) AND i.date < ?
معلمات البحث: ['2025/05/28', '2025/05/28%', '2025/05/29']
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم العثور على 0 فاتورة
تم تحديث صفحة الفواتير
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم تحديث صفحة المنتجات
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
فلتر تاريخ البدء: 2025/05/28
فلتر تاريخ الانتهاء: 2025/05/28 (قبل 2025/05/29)
استعلام البحث: 
            SELECT i.id, i.reference_number, i.date, i.subtotal, i.tax, i.discount, 
                   i.total, i.paid_amount, i.remaining_amount, i.payment_method, i.status, 
                   i.notes, c.name as customer_name, c.id as customer_id
            FROM invoices i
            LEFT JOIN customers c ON i.customer_id = c.id
            WHERE 1=1
         AND (i.date >= ? OR i.date LIKE ?) AND i.date < ?
معلمات البحث: ['2025/05/28', '2025/05/28%', '2025/05/29']
تم العثور على 0 فاتورة
تم تحديث صفحة الفواتير
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
Invoices after initial filtering (status, customer): 0
تم تحديث صفحة التقارير
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تنسيق التاريخ: 2025/05/25 14:10:01
تم تحديث صفحة العملاء
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم استرجاع 2 مورد من قاعدة البيانات
أول مورد: {'id': 10, 'name': 'احمد ربيع', 'phone': '', 'email': '', 'address': '', 'notes': '', 'contact_person': None, 'total_purchases': 5871.5, 'last_purchase_date': '2025/05/14 10:17:48', 'purchase_count': 1, 'total_debt': 2000.0}
تاريخ آخر عملية شراء للمورد احمد ربيع: 2025/05/14 10:17:48
تنسيق التاريخ: 2025/05/14 10:17:48
تاريخ آخر عملية شراء للمورد عمرو ابو رحمة: 2025/05/14 10:28:43
تنسيق التاريخ: 2025/05/14 10:28:43
تم تحديث صفحة الموردين
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم قطع الاتصال بقاعدة البيانات
تم تحديث صفحة الإعدادات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم قطع الاتصال بقاعدة البيانات
تم تحديث محتوى تاب المستخدمين
تم تحديث محتوى تاب قاعدة البيانات والنسخ الاحتياطي
تم تحديث محتوى تاب عام
تم اختيار العميل: 
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم العثور على 1 عميل
تم اختيار العميل: جاري تحميل العملاء...
تم العثور على 0 عميل
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: عميل نقدي
تم تحديث صفحة المبيعات
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
إجمالي الفاتورة: 50.0 - عدد المنتجات: 1
إجمالي الفاتورة: 0.0 - عدد المنتجات: 0
إجمالي الفاتورة: 450.0 - عدد المنتجات: 1
إجمالي الفاتورة: 900.0 - عدد المنتجات: 1
إجمالي الفاتورة: 0.0 - عدد المنتجات: 0
إجمالي الفاتورة: 140.0 - عدد المنتجات: 1
إجمالي الفاتورة: 225.0 - عدد المنتجات: 2
إجمالي الفاتورة: 290.0 - عدد المنتجات: 3
إجمالي الفاتورة: 225.0 - عدد المنتجات: 2
إجمالي الفاتورة: 140.0 - عدد المنتجات: 1
إجمالي الفاتورة: 0.0 - عدد المنتجات: 0
C:\Users\<USER>\Desktop\11\main.pyw:86: DeprecationWarning: sipPyTypeDict() is deprecated, the extension module should use sipPyTypeDictRef() instead
  class MainWindow(QMainWindow):
استخدام الخط المحمل من الملف: Readex Pro
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم إنشاء الجداول بنجاح
تم حفظ التغييرات في قاعدة البيانات
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
تم حفظ التغييرات في قاعدة البيانات
لا توجد بيانات في جدول purchase_invoices، لا حاجة للترحيل.
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
المستخدم الرئيسي موجود بالفعل
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
عرض نافذة تسجيل الدخول...
تم حفظ التغييرات في قاعدة البيانات
نتيجة تسجيل الدخول: 1
بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-28 10:51:39', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-28 10:51:39', 'status': 'نشط'}
جاري إنشاء النافذة الرئيسية...
بدء إنشاء النافذة الرئيسية...
تم استدعاء منشئ الفئة الأب
تم تخزين بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-28 10:51:39', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-28 10:51:39', 'status': 'نشط'}
جاري تعيين الصلاحيات...
جاري إنشاء StackedWidget...
تم إنشاء StackedWidget
جاري إنشاء زر وصفحة: المبيعات
تم إنشاء زر: المبيعات
جاري إنشاء صفحة: المبيعات
خطأ في معالجة المنتجات المفضلة: 'SalesView' object has no attribute 'product_buttons'
تم إنشاء صفحة: المبيعات
تم إضافة صفحة: المبيعات إلى StackedWidget
جاري إنشاء زر وصفحة: المنتجات
تم إنشاء زر: المنتجات
جاري إنشاء صفحة: المنتجات
تم إنشاء صفحة: المنتجات
تم إضافة صفحة: المنتجات إلى StackedWidget
جاري إنشاء زر وصفحة: الفواتير
تم إنشاء زر: الفواتير
جاري إنشاء صفحة: الفواتير
فلتر تاريخ البدء: 2025/05/28
فلتر تاريخ الانتهاء: 2025/05/28 (قبل 2025/05/29)
استعلام البحث: 
            SELECT i.id, i.reference_number, i.date, i.subtotal, i.tax, i.discount, 
                   i.total, i.paid_amount, i.remaining_amount, i.payment_method, i.status, 
                   i.notes, c.name as customer_name, c.id as customer_id
            FROM invoices i
            LEFT JOIN customers c ON i.customer_id = c.id
            WHERE 1=1
         AND (i.date >= ? OR i.date LIKE ?) AND i.date < ?
معلمات البحث: ['2025/05/28', '2025/05/28%', '2025/05/29']
تم العثور على 0 فاتورة
تم إنشاء صفحة: الفواتير
تم إضافة صفحة: الفواتير إلى StackedWidget
جاري إنشاء زر وصفحة: التقارير
تم إنشاء زر: التقارير
جاري إنشاء صفحة: التقارير
Invoices after initial filtering (status, customer): 0
تم إنشاء صفحة: التقارير
تم إضافة صفحة: التقارير إلى StackedWidget
جاري إنشاء زر وصفحة: العملاء
تم إنشاء زر: العملاء
جاري إنشاء صفحة: العملاء
تنسيق التاريخ: 2025/05/25 14:10:01
تم إنشاء صفحة: العملاء
تم إضافة صفحة: العملاء إلى StackedWidget
جاري إنشاء زر وصفحة: الموردين
تم إنشاء زر: الموردين
جاري إنشاء صفحة: الموردين
تم استرجاع 2 مورد من قاعدة البيانات
أول مورد: {'id': 10, 'name': 'احمد ربيع', 'phone': '', 'email': '', 'address': '', 'notes': '', 'contact_person': None, 'total_purchases': 5871.5, 'last_purchase_date': '2025/05/14 10:17:48', 'purchase_count': 1, 'total_debt': 2000.0}
تاريخ آخر عملية شراء للمورد احمد ربيع: 2025/05/14 10:17:48
تنسيق التاريخ: 2025/05/14 10:17:48
تاريخ آخر عملية شراء للمورد عمرو ابو رحمة: 2025/05/14 10:28:43
تنسيق التاريخ: 2025/05/14 10:28:43
تم إنشاء صفحة: الموردين
تم إضافة صفحة: الموردين إلى StackedWidget
جاري إنشاء زر وصفحة: المصروفات
تم إنشاء زر: المصروفات
جاري إنشاء صفحة: المصروفات
تم إنشاء صفحة: المصروفات
تم إضافة صفحة: المصروفات إلى StackedWidget
جاري إنشاء زر وصفحة: الإعدادات
تم إنشاء زر: الإعدادات
جاري إنشاء صفحة: الإعدادات
تم تحميل 2 مستخدم من قاعدة البيانات
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم إنشاء صفحة: الإعدادات
تم إضافة صفحة: الإعدادات إلى StackedWidget
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: جاري تحميل العملاء...
تم العثور على 0 عميل
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: عميل نقدي
تم تحديث صفحة المبيعات
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم قطع الاتصال بقاعدة البيانات
جاري تحديث معلومات المستخدم...
تم إنشاء النافذة الرئيسية بنجاح
تم إنشاء النافذة الرئيسية بنجاح
تم عرض النافذة الرئيسية
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
إجمالي الفاتورة: 450.0 - عدد المنتجات: 1
إجمالي الفاتورة: 0.0 - عدد المنتجات: 0
إجمالي الفاتورة: 85.0 - عدد المنتجات: 1
إجمالي الفاتورة: 195.0 - عدد المنتجات: 2
C:\Users\<USER>\Desktop\11\main.pyw:86: DeprecationWarning: sipPyTypeDict() is deprecated, the extension module should use sipPyTypeDictRef() instead
  class MainWindow(QMainWindow):
استخدام الخط المحمل من الملف: Readex Pro
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم إنشاء الجداول بنجاح
تم حفظ التغييرات في قاعدة البيانات
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
تم حفظ التغييرات في قاعدة البيانات
لا توجد بيانات في جدول purchase_invoices، لا حاجة للترحيل.
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
المستخدم الرئيسي موجود بالفعل
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
عرض نافذة تسجيل الدخول...
تم حفظ التغييرات في قاعدة البيانات
نتيجة تسجيل الدخول: 1
بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-28 11:04:22', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-28 11:04:22', 'status': 'نشط'}
جاري إنشاء النافذة الرئيسية...
بدء إنشاء النافذة الرئيسية...
تم استدعاء منشئ الفئة الأب
تم تخزين بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-28 11:04:22', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-28 11:04:22', 'status': 'نشط'}
جاري تعيين الصلاحيات...
جاري إنشاء StackedWidget...
تم إنشاء StackedWidget
جاري إنشاء زر وصفحة: المبيعات
تم إنشاء زر: المبيعات
جاري إنشاء صفحة: المبيعات
خطأ في معالجة المنتجات المفضلة: 'SalesView' object has no attribute 'product_buttons'
تم إنشاء صفحة: المبيعات
تم إضافة صفحة: المبيعات إلى StackedWidget
جاري إنشاء زر وصفحة: المنتجات
تم إنشاء زر: المنتجات
جاري إنشاء صفحة: المنتجات
تم إنشاء صفحة: المنتجات
تم إضافة صفحة: المنتجات إلى StackedWidget
جاري إنشاء زر وصفحة: الفواتير
تم إنشاء زر: الفواتير
جاري إنشاء صفحة: الفواتير
فلتر تاريخ البدء: 2025/05/28
فلتر تاريخ الانتهاء: 2025/05/28 (قبل 2025/05/29)
استعلام البحث: 
            SELECT i.id, i.reference_number, i.date, i.subtotal, i.tax, i.discount, 
                   i.total, i.paid_amount, i.remaining_amount, i.payment_method, i.status, 
                   i.notes, c.name as customer_name, c.id as customer_id
            FROM invoices i
            LEFT JOIN customers c ON i.customer_id = c.id
            WHERE 1=1
         AND (i.date >= ? OR i.date LIKE ?) AND i.date < ?
معلمات البحث: ['2025/05/28', '2025/05/28%', '2025/05/29']
تم العثور على 0 فاتورة
تم إنشاء صفحة: الفواتير
تم إضافة صفحة: الفواتير إلى StackedWidget
جاري إنشاء زر وصفحة: التقارير
تم إنشاء زر: التقارير
جاري إنشاء صفحة: التقارير
Invoices after initial filtering (status, customer): 0
تم إنشاء صفحة: التقارير
تم إضافة صفحة: التقارير إلى StackedWidget
جاري إنشاء زر وصفحة: العملاء
تم إنشاء زر: العملاء
جاري إنشاء صفحة: العملاء
تنسيق التاريخ: 2025/05/25 14:10:01
تم إنشاء صفحة: العملاء
تم إضافة صفحة: العملاء إلى StackedWidget
جاري إنشاء زر وصفحة: الموردين
تم إنشاء زر: الموردين
جاري إنشاء صفحة: الموردين
تم استرجاع 2 مورد من قاعدة البيانات
أول مورد: {'id': 10, 'name': 'احمد ربيع', 'phone': '', 'email': '', 'address': '', 'notes': '', 'contact_person': None, 'total_purchases': 5871.5, 'last_purchase_date': '2025/05/14 10:17:48', 'purchase_count': 1, 'total_debt': 2000.0}
تاريخ آخر عملية شراء للمورد احمد ربيع: 2025/05/14 10:17:48
تنسيق التاريخ: 2025/05/14 10:17:48
تاريخ آخر عملية شراء للمورد عمرو ابو رحمة: 2025/05/14 10:28:43
تنسيق التاريخ: 2025/05/14 10:28:43
تم إنشاء صفحة: الموردين
تم إضافة صفحة: الموردين إلى StackedWidget
جاري إنشاء زر وصفحة: المصروفات
تم إنشاء زر: المصروفات
جاري إنشاء صفحة: المصروفات
تم إنشاء صفحة: المصروفات
تم إضافة صفحة: المصروفات إلى StackedWidget
جاري إنشاء زر وصفحة: الإعدادات
تم إنشاء زر: الإعدادات
جاري إنشاء صفحة: الإعدادات
تم تحميل 2 مستخدم من قاعدة البيانات
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم إنشاء صفحة: الإعدادات
تم إضافة صفحة: الإعدادات إلى StackedWidget
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: جاري تحميل العملاء...
تم العثور على 0 عميل
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: عميل نقدي
تم تحديث صفحة المبيعات
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم قطع الاتصال بقاعدة البيانات
جاري تحديث معلومات المستخدم...
تم إنشاء النافذة الرئيسية بنجاح
تم إنشاء النافذة الرئيسية بنجاح
تم عرض النافذة الرئيسية
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
إجمالي الفاتورة: 85.0 - عدد المنتجات: 1
إجمالي الفاتورة: 0.0 - عدد المنتجات: 0
إجمالي الفاتورة: 85.0 - عدد المنتجات: 1
c:\Users\<USER>\Desktop\11\main.pyw:86: DeprecationWarning: sipPyTypeDict() is deprecated, the extension module should use sipPyTypeDictRef() instead
  class MainWindow(QMainWindow):
استخدام الخط المحمل من الملف: Readex Pro
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم إنشاء الجداول بنجاح
تم حفظ التغييرات في قاعدة البيانات
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
تم حفظ التغييرات في قاعدة البيانات
لا توجد بيانات في جدول purchase_invoices، لا حاجة للترحيل.
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
المستخدم الرئيسي موجود بالفعل
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
عرض نافذة تسجيل الدخول...
تم حفظ التغييرات في قاعدة البيانات
نتيجة تسجيل الدخول: 1
بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-28 11:10:09', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-28 11:10:09', 'status': 'نشط'}
جاري إنشاء النافذة الرئيسية...
بدء إنشاء النافذة الرئيسية...
تم استدعاء منشئ الفئة الأب
تم تخزين بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-28 11:10:09', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-28 11:10:09', 'status': 'نشط'}
جاري تعيين الصلاحيات...
جاري إنشاء StackedWidget...
تم إنشاء StackedWidget
جاري إنشاء زر وصفحة: المبيعات
تم إنشاء زر: المبيعات
جاري إنشاء صفحة: المبيعات
خطأ في معالجة المنتجات المفضلة: 'SalesView' object has no attribute 'product_buttons'
تم إنشاء صفحة: المبيعات
تم إضافة صفحة: المبيعات إلى StackedWidget
جاري إنشاء زر وصفحة: المنتجات
تم إنشاء زر: المنتجات
جاري إنشاء صفحة: المنتجات
تم إنشاء صفحة: المنتجات
تم إضافة صفحة: المنتجات إلى StackedWidget
جاري إنشاء زر وصفحة: الفواتير
تم إنشاء زر: الفواتير
جاري إنشاء صفحة: الفواتير
فلتر تاريخ البدء: 2025/05/28
فلتر تاريخ الانتهاء: 2025/05/28 (قبل 2025/05/29)
استعلام البحث: 
            SELECT i.id, i.reference_number, i.date, i.subtotal, i.tax, i.discount, 
                   i.total, i.paid_amount, i.remaining_amount, i.payment_method, i.status, 
                   i.notes, c.name as customer_name, c.id as customer_id
            FROM invoices i
            LEFT JOIN customers c ON i.customer_id = c.id
            WHERE 1=1
         AND (i.date >= ? OR i.date LIKE ?) AND i.date < ?
معلمات البحث: ['2025/05/28', '2025/05/28%', '2025/05/29']
تم العثور على 0 فاتورة
تم إنشاء صفحة: الفواتير
تم إضافة صفحة: الفواتير إلى StackedWidget
جاري إنشاء زر وصفحة: التقارير
تم إنشاء زر: التقارير
جاري إنشاء صفحة: التقارير
Invoices after initial filtering (status, customer): 0
تم إنشاء صفحة: التقارير
تم إضافة صفحة: التقارير إلى StackedWidget
جاري إنشاء زر وصفحة: العملاء
تم إنشاء زر: العملاء
جاري إنشاء صفحة: العملاء
تنسيق التاريخ: 2025/05/25 14:10:01
تم إنشاء صفحة: العملاء
تم إضافة صفحة: العملاء إلى StackedWidget
جاري إنشاء زر وصفحة: الموردين
تم إنشاء زر: الموردين
جاري إنشاء صفحة: الموردين
تم استرجاع 2 مورد من قاعدة البيانات
أول مورد: {'id': 10, 'name': 'احمد ربيع', 'phone': '', 'email': '', 'address': '', 'notes': '', 'contact_person': None, 'total_purchases': 5871.5, 'last_purchase_date': '2025/05/14 10:17:48', 'purchase_count': 1, 'total_debt': 2000.0}
تاريخ آخر عملية شراء للمورد احمد ربيع: 2025/05/14 10:17:48
تنسيق التاريخ: 2025/05/14 10:17:48
تاريخ آخر عملية شراء للمورد عمرو ابو رحمة: 2025/05/14 10:28:43
تنسيق التاريخ: 2025/05/14 10:28:43
تم إنشاء صفحة: الموردين
تم إضافة صفحة: الموردين إلى StackedWidget
جاري إنشاء زر وصفحة: المصروفات
تم إنشاء زر: المصروفات
جاري إنشاء صفحة: المصروفات
تم إنشاء صفحة: المصروفات
تم إضافة صفحة: المصروفات إلى StackedWidget
جاري إنشاء زر وصفحة: الإعدادات
تم إنشاء زر: الإعدادات
جاري إنشاء صفحة: الإعدادات
تم تحميل 2 مستخدم من قاعدة البيانات
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم إنشاء صفحة: الإعدادات
تم إضافة صفحة: الإعدادات إلى StackedWidget
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: جاري تحميل العملاء...
تم العثور على 0 عميل
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: عميل نقدي
تم تحديث صفحة المبيعات
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم قطع الاتصال بقاعدة البيانات
جاري تحديث معلومات المستخدم...
تم إنشاء النافذة الرئيسية بنجاح
تم إنشاء النافذة الرئيسية بنجاح
تم عرض النافذة الرئيسية
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
إجمالي الفاتورة: 160.0 - عدد المنتجات: 1
C:\Users\<USER>\Desktop\11\main.pyw:86: DeprecationWarning: sipPyTypeDict() is deprecated, the extension module should use sipPyTypeDictRef() instead
  class MainWindow(QMainWindow):
استخدام الخط المحمل من الملف: Readex Pro
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم إنشاء الجداول بنجاح
تم حفظ التغييرات في قاعدة البيانات
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
تم حفظ التغييرات في قاعدة البيانات
لا توجد بيانات في جدول purchase_invoices، لا حاجة للترحيل.
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
المستخدم الرئيسي موجود بالفعل
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
عرض نافذة تسجيل الدخول...
تم حفظ التغييرات في قاعدة البيانات
نتيجة تسجيل الدخول: 1
بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-28 11:14:01', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-28 11:14:01', 'status': 'نشط'}
جاري إنشاء النافذة الرئيسية...
بدء إنشاء النافذة الرئيسية...
تم استدعاء منشئ الفئة الأب
تم تخزين بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-28 11:14:01', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-28 11:14:01', 'status': 'نشط'}
جاري تعيين الصلاحيات...
جاري إنشاء StackedWidget...
تم إنشاء StackedWidget
جاري إنشاء زر وصفحة: المبيعات
تم إنشاء زر: المبيعات
جاري إنشاء صفحة: المبيعات
خطأ في معالجة المنتجات المفضلة: 'SalesView' object has no attribute 'product_buttons'
تم إنشاء صفحة: المبيعات
تم إضافة صفحة: المبيعات إلى StackedWidget
جاري إنشاء زر وصفحة: المنتجات
تم إنشاء زر: المنتجات
جاري إنشاء صفحة: المنتجات
تم إنشاء صفحة: المنتجات
تم إضافة صفحة: المنتجات إلى StackedWidget
جاري إنشاء زر وصفحة: الفواتير
تم إنشاء زر: الفواتير
جاري إنشاء صفحة: الفواتير
فلتر تاريخ البدء: 2025/05/28
فلتر تاريخ الانتهاء: 2025/05/28 (قبل 2025/05/29)
استعلام البحث: 
            SELECT i.id, i.reference_number, i.date, i.subtotal, i.tax, i.discount, 
                   i.total, i.paid_amount, i.remaining_amount, i.payment_method, i.status, 
                   i.notes, c.name as customer_name, c.id as customer_id
            FROM invoices i
            LEFT JOIN customers c ON i.customer_id = c.id
            WHERE 1=1
         AND (i.date >= ? OR i.date LIKE ?) AND i.date < ?
معلمات البحث: ['2025/05/28', '2025/05/28%', '2025/05/29']
تم العثور على 0 فاتورة
تم إنشاء صفحة: الفواتير
تم إضافة صفحة: الفواتير إلى StackedWidget
جاري إنشاء زر وصفحة: التقارير
تم إنشاء زر: التقارير
جاري إنشاء صفحة: التقارير
Invoices after initial filtering (status, customer): 0
تم إنشاء صفحة: التقارير
تم إضافة صفحة: التقارير إلى StackedWidget
جاري إنشاء زر وصفحة: العملاء
تم إنشاء زر: العملاء
جاري إنشاء صفحة: العملاء
تنسيق التاريخ: 2025/05/25 14:10:01
تم إنشاء صفحة: العملاء
تم إضافة صفحة: العملاء إلى StackedWidget
جاري إنشاء زر وصفحة: الموردين
تم إنشاء زر: الموردين
جاري إنشاء صفحة: الموردين
تم استرجاع 2 مورد من قاعدة البيانات
أول مورد: {'id': 10, 'name': 'احمد ربيع', 'phone': '', 'email': '', 'address': '', 'notes': '', 'contact_person': None, 'total_purchases': 5871.5, 'last_purchase_date': '2025/05/14 10:17:48', 'purchase_count': 1, 'total_debt': 2000.0}
تاريخ آخر عملية شراء للمورد احمد ربيع: 2025/05/14 10:17:48
تنسيق التاريخ: 2025/05/14 10:17:48
تاريخ آخر عملية شراء للمورد عمرو ابو رحمة: 2025/05/14 10:28:43
تنسيق التاريخ: 2025/05/14 10:28:43
تم إنشاء صفحة: الموردين
تم إضافة صفحة: الموردين إلى StackedWidget
جاري إنشاء زر وصفحة: المصروفات
تم إنشاء زر: المصروفات
جاري إنشاء صفحة: المصروفات
تم إنشاء صفحة: المصروفات
تم إضافة صفحة: المصروفات إلى StackedWidget
جاري إنشاء زر وصفحة: الإعدادات
تم إنشاء زر: الإعدادات
جاري إنشاء صفحة: الإعدادات
تم تحميل 2 مستخدم من قاعدة البيانات
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم إنشاء صفحة: الإعدادات
تم إضافة صفحة: الإعدادات إلى StackedWidget
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: جاري تحميل العملاء...
تم العثور على 0 عميل
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: عميل نقدي
تم تحديث صفحة المبيعات
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم قطع الاتصال بقاعدة البيانات
جاري تحديث معلومات المستخدم...
تم إنشاء النافذة الرئيسية بنجاح
تم إنشاء النافذة الرئيسية بنجاح
تم عرض النافذة الرئيسية
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
إجمالي الفاتورة: 110.0 - عدد المنتجات: 1
c:\Users\<USER>\Desktop\11\main.pyw:86: DeprecationWarning: sipPyTypeDict() is deprecated, the extension module should use sipPyTypeDictRef() instead
  class MainWindow(QMainWindow):
استخدام الخط المحمل من الملف: Readex Pro
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم إنشاء الجداول بنجاح
تم حفظ التغييرات في قاعدة البيانات
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
تم حفظ التغييرات في قاعدة البيانات
لا توجد بيانات في جدول purchase_invoices، لا حاجة للترحيل.
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
المستخدم الرئيسي موجود بالفعل
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
عرض نافذة تسجيل الدخول...
تم حفظ التغييرات في قاعدة البيانات
نتيجة تسجيل الدخول: 1
بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-28 11:14:15', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-28 11:14:15', 'status': 'نشط'}
جاري إنشاء النافذة الرئيسية...
بدء إنشاء النافذة الرئيسية...
تم استدعاء منشئ الفئة الأب
تم تخزين بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-28 11:14:15', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-28 11:14:15', 'status': 'نشط'}
جاري تعيين الصلاحيات...
جاري إنشاء StackedWidget...
تم إنشاء StackedWidget
جاري إنشاء زر وصفحة: المبيعات
تم إنشاء زر: المبيعات
جاري إنشاء صفحة: المبيعات
خطأ في معالجة المنتجات المفضلة: 'SalesView' object has no attribute 'product_buttons'
تم إنشاء صفحة: المبيعات
تم إضافة صفحة: المبيعات إلى StackedWidget
جاري إنشاء زر وصفحة: المنتجات
تم إنشاء زر: المنتجات
جاري إنشاء صفحة: المنتجات
تم إنشاء صفحة: المنتجات
تم إضافة صفحة: المنتجات إلى StackedWidget
جاري إنشاء زر وصفحة: الفواتير
تم إنشاء زر: الفواتير
جاري إنشاء صفحة: الفواتير
فلتر تاريخ البدء: 2025/05/28
فلتر تاريخ الانتهاء: 2025/05/28 (قبل 2025/05/29)
استعلام البحث: 
            SELECT i.id, i.reference_number, i.date, i.subtotal, i.tax, i.discount, 
                   i.total, i.paid_amount, i.remaining_amount, i.payment_method, i.status, 
                   i.notes, c.name as customer_name, c.id as customer_id
            FROM invoices i
            LEFT JOIN customers c ON i.customer_id = c.id
            WHERE 1=1
         AND (i.date >= ? OR i.date LIKE ?) AND i.date < ?
معلمات البحث: ['2025/05/28', '2025/05/28%', '2025/05/29']
تم العثور على 0 فاتورة
تم إنشاء صفحة: الفواتير
تم إضافة صفحة: الفواتير إلى StackedWidget
جاري إنشاء زر وصفحة: التقارير
تم إنشاء زر: التقارير
جاري إنشاء صفحة: التقارير
Invoices after initial filtering (status, customer): 0
تم إنشاء صفحة: التقارير
تم إضافة صفحة: التقارير إلى StackedWidget
جاري إنشاء زر وصفحة: العملاء
تم إنشاء زر: العملاء
جاري إنشاء صفحة: العملاء
تنسيق التاريخ: 2025/05/25 14:10:01
تم إنشاء صفحة: العملاء
تم إضافة صفحة: العملاء إلى StackedWidget
جاري إنشاء زر وصفحة: الموردين
تم إنشاء زر: الموردين
جاري إنشاء صفحة: الموردين
تم استرجاع 2 مورد من قاعدة البيانات
أول مورد: {'id': 10, 'name': 'احمد ربيع', 'phone': '', 'email': '', 'address': '', 'notes': '', 'contact_person': None, 'total_purchases': 5871.5, 'last_purchase_date': '2025/05/14 10:17:48', 'purchase_count': 1, 'total_debt': 2000.0}
تاريخ آخر عملية شراء للمورد احمد ربيع: 2025/05/14 10:17:48
تنسيق التاريخ: 2025/05/14 10:17:48
تاريخ آخر عملية شراء للمورد عمرو ابو رحمة: 2025/05/14 10:28:43
تنسيق التاريخ: 2025/05/14 10:28:43
تم إنشاء صفحة: الموردين
تم إضافة صفحة: الموردين إلى StackedWidget
جاري إنشاء زر وصفحة: المصروفات
تم إنشاء زر: المصروفات
جاري إنشاء صفحة: المصروفات
تم إنشاء صفحة: المصروفات
تم إضافة صفحة: المصروفات إلى StackedWidget
جاري إنشاء زر وصفحة: الإعدادات
تم إنشاء زر: الإعدادات
جاري إنشاء صفحة: الإعدادات
تم تحميل 2 مستخدم من قاعدة البيانات
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم إنشاء صفحة: الإعدادات
تم إضافة صفحة: الإعدادات إلى StackedWidget
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: جاري تحميل العملاء...
تم العثور على 0 عميل
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: عميل نقدي
تم تحديث صفحة المبيعات
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم قطع الاتصال بقاعدة البيانات
جاري تحديث معلومات المستخدم...
تم إنشاء النافذة الرئيسية بنجاح
تم إنشاء النافذة الرئيسية بنجاح
تم عرض النافذة الرئيسية
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
إجمالي الفاتورة: 160.0 - عدد المنتجات: 1
c:\Users\<USER>\Desktop\11\main.pyw:86: DeprecationWarning: sipPyTypeDict() is deprecated, the extension module should use sipPyTypeDictRef() instead
  class MainWindow(QMainWindow):
استخدام الخط المحمل من الملف: Readex Pro
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم إنشاء الجداول بنجاح
تم حفظ التغييرات في قاعدة البيانات
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
تم حفظ التغييرات في قاعدة البيانات
لا توجد بيانات في جدول purchase_invoices، لا حاجة للترحيل.
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
المستخدم الرئيسي موجود بالفعل
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
عرض نافذة تسجيل الدخول...
نتيجة تسجيل الدخول: 0
بيانات المستخدم: None
فشل تسجيل الدخول أو تم الإلغاء
C:\Users\<USER>\Desktop\11\main.pyw:86: DeprecationWarning: sipPyTypeDict() is deprecated, the extension module should use sipPyTypeDictRef() instead
  class MainWindow(QMainWindow):
استخدام الخط المحمل من الملف: Readex Pro
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم إنشاء الجداول بنجاح
تم حفظ التغييرات في قاعدة البيانات
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
تم حفظ التغييرات في قاعدة البيانات
لا توجد بيانات في جدول purchase_invoices، لا حاجة للترحيل.
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
المستخدم الرئيسي موجود بالفعل
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
عرض نافذة تسجيل الدخول...
تم حفظ التغييرات في قاعدة البيانات
نتيجة تسجيل الدخول: 1
بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-28 11:17:46', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-28 11:17:46', 'status': 'نشط'}
جاري إنشاء النافذة الرئيسية...
بدء إنشاء النافذة الرئيسية...
تم استدعاء منشئ الفئة الأب
تم تخزين بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-28 11:17:46', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-28 11:17:46', 'status': 'نشط'}
جاري تعيين الصلاحيات...
جاري إنشاء StackedWidget...
تم إنشاء StackedWidget
جاري إنشاء زر وصفحة: المبيعات
تم إنشاء زر: المبيعات
جاري إنشاء صفحة: المبيعات
خطأ في معالجة المنتجات المفضلة: 'SalesView' object has no attribute 'product_buttons'
تم إنشاء صفحة: المبيعات
تم إضافة صفحة: المبيعات إلى StackedWidget
جاري إنشاء زر وصفحة: المنتجات
تم إنشاء زر: المنتجات
جاري إنشاء صفحة: المنتجات
تم إنشاء صفحة: المنتجات
تم إضافة صفحة: المنتجات إلى StackedWidget
جاري إنشاء زر وصفحة: الفواتير
تم إنشاء زر: الفواتير
جاري إنشاء صفحة: الفواتير
فلتر تاريخ البدء: 2025/05/28
فلتر تاريخ الانتهاء: 2025/05/28 (قبل 2025/05/29)
استعلام البحث: 
            SELECT i.id, i.reference_number, i.date, i.subtotal, i.tax, i.discount, 
                   i.total, i.paid_amount, i.remaining_amount, i.payment_method, i.status, 
                   i.notes, c.name as customer_name, c.id as customer_id
            FROM invoices i
            LEFT JOIN customers c ON i.customer_id = c.id
            WHERE 1=1
         AND (i.date >= ? OR i.date LIKE ?) AND i.date < ?
معلمات البحث: ['2025/05/28', '2025/05/28%', '2025/05/29']
تم العثور على 0 فاتورة
تم إنشاء صفحة: الفواتير
تم إضافة صفحة: الفواتير إلى StackedWidget
جاري إنشاء زر وصفحة: التقارير
تم إنشاء زر: التقارير
جاري إنشاء صفحة: التقارير
Invoices after initial filtering (status, customer): 0
تم إنشاء صفحة: التقارير
تم إضافة صفحة: التقارير إلى StackedWidget
جاري إنشاء زر وصفحة: العملاء
تم إنشاء زر: العملاء
جاري إنشاء صفحة: العملاء
تنسيق التاريخ: 2025/05/25 14:10:01
تم إنشاء صفحة: العملاء
تم إضافة صفحة: العملاء إلى StackedWidget
جاري إنشاء زر وصفحة: الموردين
تم إنشاء زر: الموردين
جاري إنشاء صفحة: الموردين
تم استرجاع 2 مورد من قاعدة البيانات
أول مورد: {'id': 10, 'name': 'احمد ربيع', 'phone': '', 'email': '', 'address': '', 'notes': '', 'contact_person': None, 'total_purchases': 5871.5, 'last_purchase_date': '2025/05/14 10:17:48', 'purchase_count': 1, 'total_debt': 2000.0}
تاريخ آخر عملية شراء للمورد احمد ربيع: 2025/05/14 10:17:48
تنسيق التاريخ: 2025/05/14 10:17:48
تاريخ آخر عملية شراء للمورد عمرو ابو رحمة: 2025/05/14 10:28:43
تنسيق التاريخ: 2025/05/14 10:28:43
تم إنشاء صفحة: الموردين
تم إضافة صفحة: الموردين إلى StackedWidget
جاري إنشاء زر وصفحة: المصروفات
تم إنشاء زر: المصروفات
جاري إنشاء صفحة: المصروفات
تم إنشاء صفحة: المصروفات
تم إضافة صفحة: المصروفات إلى StackedWidget
جاري إنشاء زر وصفحة: الإعدادات
تم إنشاء زر: الإعدادات
جاري إنشاء صفحة: الإعدادات
تم تحميل 2 مستخدم من قاعدة البيانات
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم إنشاء صفحة: الإعدادات
تم إضافة صفحة: الإعدادات إلى StackedWidget
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: جاري تحميل العملاء...
تم العثور على 0 عميل
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: عميل نقدي
تم تحديث صفحة المبيعات
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم قطع الاتصال بقاعدة البيانات
جاري تحديث معلومات المستخدم...
تم إنشاء النافذة الرئيسية بنجاح
تم إنشاء النافذة الرئيسية بنجاح
تم عرض النافذة الرئيسية
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
إجمالي الفاتورة: 110.0 - عدد المنتجات: 1
إجمالي الفاتورة: 0.0 - عدد المنتجات: 0
C:\Users\<USER>\Desktop\11\main.pyw:86: DeprecationWarning: sipPyTypeDict() is deprecated, the extension module should use sipPyTypeDictRef() instead
  class MainWindow(QMainWindow):
استخدام الخط المحمل من الملف: Readex Pro
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم إنشاء الجداول بنجاح
تم حفظ التغييرات في قاعدة البيانات
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
تم حفظ التغييرات في قاعدة البيانات
لا توجد بيانات في جدول purchase_invoices، لا حاجة للترحيل.
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
المستخدم الرئيسي موجود بالفعل
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
عرض نافذة تسجيل الدخول...
تم حفظ التغييرات في قاعدة البيانات
نتيجة تسجيل الدخول: 1
بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-28 11:18:58', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-28 11:18:58', 'status': 'نشط'}
جاري إنشاء النافذة الرئيسية...
بدء إنشاء النافذة الرئيسية...
تم استدعاء منشئ الفئة الأب
تم تخزين بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-28 11:18:58', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-28 11:18:58', 'status': 'نشط'}
جاري تعيين الصلاحيات...
جاري إنشاء StackedWidget...
تم إنشاء StackedWidget
جاري إنشاء زر وصفحة: المبيعات
تم إنشاء زر: المبيعات
جاري إنشاء صفحة: المبيعات
خطأ في معالجة المنتجات المفضلة: 'SalesView' object has no attribute 'product_buttons'
تم إنشاء صفحة: المبيعات
تم إضافة صفحة: المبيعات إلى StackedWidget
جاري إنشاء زر وصفحة: المنتجات
تم إنشاء زر: المنتجات
جاري إنشاء صفحة: المنتجات
تم إنشاء صفحة: المنتجات
تم إضافة صفحة: المنتجات إلى StackedWidget
جاري إنشاء زر وصفحة: الفواتير
تم إنشاء زر: الفواتير
جاري إنشاء صفحة: الفواتير
فلتر تاريخ البدء: 2025/05/28
فلتر تاريخ الانتهاء: 2025/05/28 (قبل 2025/05/29)
استعلام البحث: 
            SELECT i.id, i.reference_number, i.date, i.subtotal, i.tax, i.discount, 
                   i.total, i.paid_amount, i.remaining_amount, i.payment_method, i.status, 
                   i.notes, c.name as customer_name, c.id as customer_id
            FROM invoices i
            LEFT JOIN customers c ON i.customer_id = c.id
            WHERE 1=1
         AND (i.date >= ? OR i.date LIKE ?) AND i.date < ?
معلمات البحث: ['2025/05/28', '2025/05/28%', '2025/05/29']
تم العثور على 0 فاتورة
تم إنشاء صفحة: الفواتير
تم إضافة صفحة: الفواتير إلى StackedWidget
جاري إنشاء زر وصفحة: التقارير
تم إنشاء زر: التقارير
جاري إنشاء صفحة: التقارير
Invoices after initial filtering (status, customer): 0
تم إنشاء صفحة: التقارير
تم إضافة صفحة: التقارير إلى StackedWidget
جاري إنشاء زر وصفحة: العملاء
تم إنشاء زر: العملاء
جاري إنشاء صفحة: العملاء
تنسيق التاريخ: 2025/05/25 14:10:01
تم إنشاء صفحة: العملاء
تم إضافة صفحة: العملاء إلى StackedWidget
جاري إنشاء زر وصفحة: الموردين
تم إنشاء زر: الموردين
جاري إنشاء صفحة: الموردين
تم استرجاع 2 مورد من قاعدة البيانات
أول مورد: {'id': 10, 'name': 'احمد ربيع', 'phone': '', 'email': '', 'address': '', 'notes': '', 'contact_person': None, 'total_purchases': 5871.5, 'last_purchase_date': '2025/05/14 10:17:48', 'purchase_count': 1, 'total_debt': 2000.0}
تاريخ آخر عملية شراء للمورد احمد ربيع: 2025/05/14 10:17:48
تنسيق التاريخ: 2025/05/14 10:17:48
تاريخ آخر عملية شراء للمورد عمرو ابو رحمة: 2025/05/14 10:28:43
تنسيق التاريخ: 2025/05/14 10:28:43
تم إنشاء صفحة: الموردين
تم إضافة صفحة: الموردين إلى StackedWidget
جاري إنشاء زر وصفحة: المصروفات
تم إنشاء زر: المصروفات
جاري إنشاء صفحة: المصروفات
تم إنشاء صفحة: المصروفات
تم إضافة صفحة: المصروفات إلى StackedWidget
جاري إنشاء زر وصفحة: الإعدادات
تم إنشاء زر: الإعدادات
جاري إنشاء صفحة: الإعدادات
تم تحميل 2 مستخدم من قاعدة البيانات
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم إنشاء صفحة: الإعدادات
تم إضافة صفحة: الإعدادات إلى StackedWidget
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: جاري تحميل العملاء...
تم العثور على 0 عميل
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: عميل نقدي
تم تحديث صفحة المبيعات
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم قطع الاتصال بقاعدة البيانات
جاري تحديث معلومات المستخدم...
تم إنشاء النافذة الرئيسية بنجاح
تم إنشاء النافذة الرئيسية بنجاح
تم عرض النافذة الرئيسية
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
إجمالي الفاتورة: 160.0 - عدد المنتجات: 1
c:\Users\<USER>\Desktop\11\main.pyw:86: DeprecationWarning: sipPyTypeDict() is deprecated, the extension module should use sipPyTypeDictRef() instead
  class MainWindow(QMainWindow):
استخدام الخط المحمل من الملف: Readex Pro
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم إنشاء الجداول بنجاح
تم حفظ التغييرات في قاعدة البيانات
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
تم حفظ التغييرات في قاعدة البيانات
لا توجد بيانات في جدول purchase_invoices، لا حاجة للترحيل.
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
المستخدم الرئيسي موجود بالفعل
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
عرض نافذة تسجيل الدخول...
نتيجة تسجيل الدخول: 0
بيانات المستخدم: None
فشل تسجيل الدخول أو تم الإلغاء
c:\Users\<USER>\Desktop\11\main.pyw:86: DeprecationWarning: sipPyTypeDict() is deprecated, the extension module should use sipPyTypeDictRef() instead
  class MainWindow(QMainWindow):
استخدام الخط المحمل من الملف: Readex Pro
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم إنشاء الجداول بنجاح
تم حفظ التغييرات في قاعدة البيانات
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
تم حفظ التغييرات في قاعدة البيانات
لا توجد بيانات في جدول purchase_invoices، لا حاجة للترحيل.
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
المستخدم الرئيسي موجود بالفعل
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
عرض نافذة تسجيل الدخول...
تم حفظ التغييرات في قاعدة البيانات
نتيجة تسجيل الدخول: 1
بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-28 11:23:28', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-28 11:23:28', 'status': 'نشط'}
جاري إنشاء النافذة الرئيسية...
بدء إنشاء النافذة الرئيسية...
تم استدعاء منشئ الفئة الأب
تم تخزين بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-28 11:23:28', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-28 11:23:28', 'status': 'نشط'}
جاري تعيين الصلاحيات...
جاري إنشاء StackedWidget...
تم إنشاء StackedWidget
جاري إنشاء زر وصفحة: المبيعات
تم إنشاء زر: المبيعات
جاري إنشاء صفحة: المبيعات
خطأ في معالجة المنتجات المفضلة: 'SalesView' object has no attribute 'product_buttons'
تم إنشاء صفحة: المبيعات
تم إضافة صفحة: المبيعات إلى StackedWidget
جاري إنشاء زر وصفحة: المنتجات
تم إنشاء زر: المنتجات
جاري إنشاء صفحة: المنتجات
تم إنشاء صفحة: المنتجات
تم إضافة صفحة: المنتجات إلى StackedWidget
جاري إنشاء زر وصفحة: الفواتير
تم إنشاء زر: الفواتير
جاري إنشاء صفحة: الفواتير
فلتر تاريخ البدء: 2025/05/28
فلتر تاريخ الانتهاء: 2025/05/28 (قبل 2025/05/29)
استعلام البحث: 
            SELECT i.id, i.reference_number, i.date, i.subtotal, i.tax, i.discount, 
                   i.total, i.paid_amount, i.remaining_amount, i.payment_method, i.status, 
                   i.notes, c.name as customer_name, c.id as customer_id
            FROM invoices i
            LEFT JOIN customers c ON i.customer_id = c.id
            WHERE 1=1
         AND (i.date >= ? OR i.date LIKE ?) AND i.date < ?
معلمات البحث: ['2025/05/28', '2025/05/28%', '2025/05/29']
تم العثور على 0 فاتورة
تم إنشاء صفحة: الفواتير
تم إضافة صفحة: الفواتير إلى StackedWidget
جاري إنشاء زر وصفحة: التقارير
تم إنشاء زر: التقارير
جاري إنشاء صفحة: التقارير
Invoices after initial filtering (status, customer): 0
تم إنشاء صفحة: التقارير
تم إضافة صفحة: التقارير إلى StackedWidget
جاري إنشاء زر وصفحة: العملاء
تم إنشاء زر: العملاء
جاري إنشاء صفحة: العملاء
تنسيق التاريخ: 2025/05/25 14:10:01
تم إنشاء صفحة: العملاء
تم إضافة صفحة: العملاء إلى StackedWidget
جاري إنشاء زر وصفحة: الموردين
تم إنشاء زر: الموردين
جاري إنشاء صفحة: الموردين
تم استرجاع 2 مورد من قاعدة البيانات
أول مورد: {'id': 10, 'name': 'احمد ربيع', 'phone': '', 'email': '', 'address': '', 'notes': '', 'contact_person': None, 'total_purchases': 5871.5, 'last_purchase_date': '2025/05/14 10:17:48', 'purchase_count': 1, 'total_debt': 2000.0}
تاريخ آخر عملية شراء للمورد احمد ربيع: 2025/05/14 10:17:48
تنسيق التاريخ: 2025/05/14 10:17:48
تاريخ آخر عملية شراء للمورد عمرو ابو رحمة: 2025/05/14 10:28:43
تنسيق التاريخ: 2025/05/14 10:28:43
تم إنشاء صفحة: الموردين
تم إضافة صفحة: الموردين إلى StackedWidget
جاري إنشاء زر وصفحة: المصروفات
تم إنشاء زر: المصروفات
جاري إنشاء صفحة: المصروفات
تم إنشاء صفحة: المصروفات
تم إضافة صفحة: المصروفات إلى StackedWidget
جاري إنشاء زر وصفحة: الإعدادات
تم إنشاء زر: الإعدادات
جاري إنشاء صفحة: الإعدادات
تم تحميل 2 مستخدم من قاعدة البيانات
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم إنشاء صفحة: الإعدادات
تم إضافة صفحة: الإعدادات إلى StackedWidget
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: جاري تحميل العملاء...
تم العثور على 0 عميل
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: عميل نقدي
تم تحديث صفحة المبيعات
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم قطع الاتصال بقاعدة البيانات
جاري تحديث معلومات المستخدم...
تم إنشاء النافذة الرئيسية بنجاح
تم إنشاء النافذة الرئيسية بنجاح
تم عرض النافذة الرئيسية
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
إجمالي الفاتورة: 450.0 - عدد المنتجات: 1
C:\Users\<USER>\Desktop\11\main.pyw:86: DeprecationWarning: sipPyTypeDict() is deprecated, the extension module should use sipPyTypeDictRef() instead
  class MainWindow(QMainWindow):
استخدام الخط المحمل من الملف: Readex Pro
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم إنشاء الجداول بنجاح
تم حفظ التغييرات في قاعدة البيانات
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
تم حفظ التغييرات في قاعدة البيانات
لا توجد بيانات في جدول purchase_invoices، لا حاجة للترحيل.
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
المستخدم الرئيسي موجود بالفعل
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
عرض نافذة تسجيل الدخول...
تم حفظ التغييرات في قاعدة البيانات
نتيجة تسجيل الدخول: 1
بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-28 11:24:44', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-28 11:24:44', 'status': 'نشط'}
جاري إنشاء النافذة الرئيسية...
بدء إنشاء النافذة الرئيسية...
تم استدعاء منشئ الفئة الأب
تم تخزين بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-28 11:24:44', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-28 11:24:44', 'status': 'نشط'}
جاري تعيين الصلاحيات...
جاري إنشاء StackedWidget...
تم إنشاء StackedWidget
جاري إنشاء زر وصفحة: المبيعات
تم إنشاء زر: المبيعات
جاري إنشاء صفحة: المبيعات
خطأ في معالجة المنتجات المفضلة: 'SalesView' object has no attribute 'product_buttons'
تم إنشاء صفحة: المبيعات
تم إضافة صفحة: المبيعات إلى StackedWidget
جاري إنشاء زر وصفحة: المنتجات
تم إنشاء زر: المنتجات
جاري إنشاء صفحة: المنتجات
تم إنشاء صفحة: المنتجات
تم إضافة صفحة: المنتجات إلى StackedWidget
جاري إنشاء زر وصفحة: الفواتير
تم إنشاء زر: الفواتير
جاري إنشاء صفحة: الفواتير
فلتر تاريخ البدء: 2025/05/28
فلتر تاريخ الانتهاء: 2025/05/28 (قبل 2025/05/29)
استعلام البحث: 
            SELECT i.id, i.reference_number, i.date, i.subtotal, i.tax, i.discount, 
                   i.total, i.paid_amount, i.remaining_amount, i.payment_method, i.status, 
                   i.notes, c.name as customer_name, c.id as customer_id
            FROM invoices i
            LEFT JOIN customers c ON i.customer_id = c.id
            WHERE 1=1
         AND (i.date >= ? OR i.date LIKE ?) AND i.date < ?
معلمات البحث: ['2025/05/28', '2025/05/28%', '2025/05/29']
تم العثور على 0 فاتورة
تم إنشاء صفحة: الفواتير
تم إضافة صفحة: الفواتير إلى StackedWidget
جاري إنشاء زر وصفحة: التقارير
تم إنشاء زر: التقارير
جاري إنشاء صفحة: التقارير
Invoices after initial filtering (status, customer): 0
تم إنشاء صفحة: التقارير
تم إضافة صفحة: التقارير إلى StackedWidget
جاري إنشاء زر وصفحة: العملاء
تم إنشاء زر: العملاء
جاري إنشاء صفحة: العملاء
تنسيق التاريخ: 2025/05/25 14:10:01
تم إنشاء صفحة: العملاء
تم إضافة صفحة: العملاء إلى StackedWidget
جاري إنشاء زر وصفحة: الموردين
تم إنشاء زر: الموردين
جاري إنشاء صفحة: الموردين
تم استرجاع 2 مورد من قاعدة البيانات
أول مورد: {'id': 10, 'name': 'احمد ربيع', 'phone': '', 'email': '', 'address': '', 'notes': '', 'contact_person': None, 'total_purchases': 5871.5, 'last_purchase_date': '2025/05/14 10:17:48', 'purchase_count': 1, 'total_debt': 2000.0}
تاريخ آخر عملية شراء للمورد احمد ربيع: 2025/05/14 10:17:48
تنسيق التاريخ: 2025/05/14 10:17:48
تاريخ آخر عملية شراء للمورد عمرو ابو رحمة: 2025/05/14 10:28:43
تنسيق التاريخ: 2025/05/14 10:28:43
تم إنشاء صفحة: الموردين
تم إضافة صفحة: الموردين إلى StackedWidget
جاري إنشاء زر وصفحة: المصروفات
تم إنشاء زر: المصروفات
جاري إنشاء صفحة: المصروفات
تم إنشاء صفحة: المصروفات
تم إضافة صفحة: المصروفات إلى StackedWidget
جاري إنشاء زر وصفحة: الإعدادات
تم إنشاء زر: الإعدادات
جاري إنشاء صفحة: الإعدادات
تم تحميل 2 مستخدم من قاعدة البيانات
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم إنشاء صفحة: الإعدادات
تم إضافة صفحة: الإعدادات إلى StackedWidget
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: جاري تحميل العملاء...
تم العثور على 0 عميل
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: عميل نقدي
تم تحديث صفحة المبيعات
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم قطع الاتصال بقاعدة البيانات
جاري تحديث معلومات المستخدم...
تم إنشاء النافذة الرئيسية بنجاح
تم إنشاء النافذة الرئيسية بنجاح
تم عرض النافذة الرئيسية
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
إجمالي الفاتورة: 160.0 - عدد المنتجات: 1
c:\Users\<USER>\Desktop\11\main.pyw:86: DeprecationWarning: sipPyTypeDict() is deprecated, the extension module should use sipPyTypeDictRef() instead
  class MainWindow(QMainWindow):
استخدام الخط المحمل من الملف: Readex Pro
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم إنشاء الجداول بنجاح
تم حفظ التغييرات في قاعدة البيانات
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
تم حفظ التغييرات في قاعدة البيانات
لا توجد بيانات في جدول purchase_invoices، لا حاجة للترحيل.
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
المستخدم الرئيسي موجود بالفعل
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
عرض نافذة تسجيل الدخول...
تم حفظ التغييرات في قاعدة البيانات
نتيجة تسجيل الدخول: 1
بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-28 11:27:42', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-28 11:27:42', 'status': 'نشط'}
جاري إنشاء النافذة الرئيسية...
بدء إنشاء النافذة الرئيسية...
تم استدعاء منشئ الفئة الأب
تم تخزين بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-28 11:27:42', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-28 11:27:42', 'status': 'نشط'}
جاري تعيين الصلاحيات...
جاري إنشاء StackedWidget...
تم إنشاء StackedWidget
جاري إنشاء زر وصفحة: المبيعات
تم إنشاء زر: المبيعات
جاري إنشاء صفحة: المبيعات
خطأ في معالجة المنتجات المفضلة: 'SalesView' object has no attribute 'product_buttons'
تم إنشاء صفحة: المبيعات
تم إضافة صفحة: المبيعات إلى StackedWidget
جاري إنشاء زر وصفحة: المنتجات
تم إنشاء زر: المنتجات
جاري إنشاء صفحة: المنتجات
تم إنشاء صفحة: المنتجات
تم إضافة صفحة: المنتجات إلى StackedWidget
جاري إنشاء زر وصفحة: الفواتير
تم إنشاء زر: الفواتير
جاري إنشاء صفحة: الفواتير
فلتر تاريخ البدء: 2025/05/28
فلتر تاريخ الانتهاء: 2025/05/28 (قبل 2025/05/29)
استعلام البحث: 
            SELECT i.id, i.reference_number, i.date, i.subtotal, i.tax, i.discount, 
                   i.total, i.paid_amount, i.remaining_amount, i.payment_method, i.status, 
                   i.notes, c.name as customer_name, c.id as customer_id
            FROM invoices i
            LEFT JOIN customers c ON i.customer_id = c.id
            WHERE 1=1
         AND (i.date >= ? OR i.date LIKE ?) AND i.date < ?
معلمات البحث: ['2025/05/28', '2025/05/28%', '2025/05/29']
تم العثور على 0 فاتورة
تم إنشاء صفحة: الفواتير
تم إضافة صفحة: الفواتير إلى StackedWidget
جاري إنشاء زر وصفحة: التقارير
تم إنشاء زر: التقارير
جاري إنشاء صفحة: التقارير
Invoices after initial filtering (status, customer): 0
تم إنشاء صفحة: التقارير
تم إضافة صفحة: التقارير إلى StackedWidget
جاري إنشاء زر وصفحة: العملاء
تم إنشاء زر: العملاء
جاري إنشاء صفحة: العملاء
تنسيق التاريخ: 2025/05/25 14:10:01
تم إنشاء صفحة: العملاء
تم إضافة صفحة: العملاء إلى StackedWidget
جاري إنشاء زر وصفحة: الموردين
تم إنشاء زر: الموردين
جاري إنشاء صفحة: الموردين
تم استرجاع 2 مورد من قاعدة البيانات
أول مورد: {'id': 10, 'name': 'احمد ربيع', 'phone': '', 'email': '', 'address': '', 'notes': '', 'contact_person': None, 'total_purchases': 5871.5, 'last_purchase_date': '2025/05/14 10:17:48', 'purchase_count': 1, 'total_debt': 2000.0}
تاريخ آخر عملية شراء للمورد احمد ربيع: 2025/05/14 10:17:48
تنسيق التاريخ: 2025/05/14 10:17:48
تاريخ آخر عملية شراء للمورد عمرو ابو رحمة: 2025/05/14 10:28:43
تنسيق التاريخ: 2025/05/14 10:28:43
تم إنشاء صفحة: الموردين
تم إضافة صفحة: الموردين إلى StackedWidget
جاري إنشاء زر وصفحة: المصروفات
تم إنشاء زر: المصروفات
جاري إنشاء صفحة: المصروفات
تم إنشاء صفحة: المصروفات
تم إضافة صفحة: المصروفات إلى StackedWidget
جاري إنشاء زر وصفحة: الإعدادات
تم إنشاء زر: الإعدادات
جاري إنشاء صفحة: الإعدادات
تم تحميل 2 مستخدم من قاعدة البيانات
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم إنشاء صفحة: الإعدادات
تم إضافة صفحة: الإعدادات إلى StackedWidget
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: جاري تحميل العملاء...
تم العثور على 0 عميل
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: عميل نقدي
تم تحديث صفحة المبيعات
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم قطع الاتصال بقاعدة البيانات
جاري تحديث معلومات المستخدم...
تم إنشاء النافذة الرئيسية بنجاح
تم إنشاء النافذة الرئيسية بنجاح
تم عرض النافذة الرئيسية
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
إجمالي الفاتورة: 140.0 - عدد المنتجات: 1
إجمالي الفاتورة: 300.0 - عدد المنتجات: 2
C:\Users\<USER>\Desktop\11\main.pyw:86: DeprecationWarning: sipPyTypeDict() is deprecated, the extension module should use sipPyTypeDictRef() instead
  class MainWindow(QMainWindow):
استخدام الخط المحمل من الملف: Readex Pro
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم إنشاء الجداول بنجاح
تم حفظ التغييرات في قاعدة البيانات
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
تم حفظ التغييرات في قاعدة البيانات
لا توجد بيانات في جدول purchase_invoices، لا حاجة للترحيل.
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
المستخدم الرئيسي موجود بالفعل
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
عرض نافذة تسجيل الدخول...
تم حفظ التغييرات في قاعدة البيانات
نتيجة تسجيل الدخول: 1
بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-28 11:29:13', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-28 11:29:13', 'status': 'نشط'}
جاري إنشاء النافذة الرئيسية...
بدء إنشاء النافذة الرئيسية...
تم استدعاء منشئ الفئة الأب
تم تخزين بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-28 11:29:13', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-28 11:29:13', 'status': 'نشط'}
جاري تعيين الصلاحيات...
جاري إنشاء StackedWidget...
تم إنشاء StackedWidget
جاري إنشاء زر وصفحة: المبيعات
تم إنشاء زر: المبيعات
جاري إنشاء صفحة: المبيعات
خطأ في معالجة المنتجات المفضلة: 'SalesView' object has no attribute 'product_buttons'
تم إنشاء صفحة: المبيعات
تم إضافة صفحة: المبيعات إلى StackedWidget
جاري إنشاء زر وصفحة: المنتجات
تم إنشاء زر: المنتجات
جاري إنشاء صفحة: المنتجات
تم إنشاء صفحة: المنتجات
تم إضافة صفحة: المنتجات إلى StackedWidget
جاري إنشاء زر وصفحة: الفواتير
تم إنشاء زر: الفواتير
جاري إنشاء صفحة: الفواتير
فلتر تاريخ البدء: 2025/05/28
فلتر تاريخ الانتهاء: 2025/05/28 (قبل 2025/05/29)
استعلام البحث: 
            SELECT i.id, i.reference_number, i.date, i.subtotal, i.tax, i.discount, 
                   i.total, i.paid_amount, i.remaining_amount, i.payment_method, i.status, 
                   i.notes, c.name as customer_name, c.id as customer_id
            FROM invoices i
            LEFT JOIN customers c ON i.customer_id = c.id
            WHERE 1=1
         AND (i.date >= ? OR i.date LIKE ?) AND i.date < ?
معلمات البحث: ['2025/05/28', '2025/05/28%', '2025/05/29']
تم العثور على 0 فاتورة
تم إنشاء صفحة: الفواتير
تم إضافة صفحة: الفواتير إلى StackedWidget
جاري إنشاء زر وصفحة: التقارير
تم إنشاء زر: التقارير
جاري إنشاء صفحة: التقارير
Invoices after initial filtering (status, customer): 0
تم إنشاء صفحة: التقارير
تم إضافة صفحة: التقارير إلى StackedWidget
جاري إنشاء زر وصفحة: العملاء
تم إنشاء زر: العملاء
جاري إنشاء صفحة: العملاء
تنسيق التاريخ: 2025/05/25 14:10:01
تم إنشاء صفحة: العملاء
تم إضافة صفحة: العملاء إلى StackedWidget
جاري إنشاء زر وصفحة: الموردين
تم إنشاء زر: الموردين
جاري إنشاء صفحة: الموردين
تم استرجاع 2 مورد من قاعدة البيانات
أول مورد: {'id': 10, 'name': 'احمد ربيع', 'phone': '', 'email': '', 'address': '', 'notes': '', 'contact_person': None, 'total_purchases': 5871.5, 'last_purchase_date': '2025/05/14 10:17:48', 'purchase_count': 1, 'total_debt': 2000.0}
تاريخ آخر عملية شراء للمورد احمد ربيع: 2025/05/14 10:17:48
تنسيق التاريخ: 2025/05/14 10:17:48
تاريخ آخر عملية شراء للمورد عمرو ابو رحمة: 2025/05/14 10:28:43
تنسيق التاريخ: 2025/05/14 10:28:43
تم إنشاء صفحة: الموردين
تم إضافة صفحة: الموردين إلى StackedWidget
جاري إنشاء زر وصفحة: المصروفات
تم إنشاء زر: المصروفات
جاري إنشاء صفحة: المصروفات
تم إنشاء صفحة: المصروفات
تم إضافة صفحة: المصروفات إلى StackedWidget
جاري إنشاء زر وصفحة: الإعدادات
تم إنشاء زر: الإعدادات
جاري إنشاء صفحة: الإعدادات
تم تحميل 2 مستخدم من قاعدة البيانات
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم إنشاء صفحة: الإعدادات
تم إضافة صفحة: الإعدادات إلى StackedWidget
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: جاري تحميل العملاء...
تم العثور على 0 عميل
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: عميل نقدي
تم تحديث صفحة المبيعات
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم قطع الاتصال بقاعدة البيانات
جاري تحديث معلومات المستخدم...
تم إنشاء النافذة الرئيسية بنجاح
تم إنشاء النافذة الرئيسية بنجاح
تم عرض النافذة الرئيسية
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
إجمالي الفاتورة: 110.0 - عدد المنتجات: 1
c:\Users\<USER>\Desktop\11\main.pyw:86: DeprecationWarning: sipPyTypeDict() is deprecated, the extension module should use sipPyTypeDictRef() instead
  class MainWindow(QMainWindow):
استخدام الخط المحمل من الملف: Readex Pro
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم إنشاء الجداول بنجاح
تم حفظ التغييرات في قاعدة البيانات
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
تم حفظ التغييرات في قاعدة البيانات
لا توجد بيانات في جدول purchase_invoices، لا حاجة للترحيل.
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
المستخدم الرئيسي موجود بالفعل
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
عرض نافذة تسجيل الدخول...
تم حفظ التغييرات في قاعدة البيانات
نتيجة تسجيل الدخول: 1
بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-28 11:31:31', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-28 11:31:31', 'status': 'نشط'}
جاري إنشاء النافذة الرئيسية...
بدء إنشاء النافذة الرئيسية...
تم استدعاء منشئ الفئة الأب
تم تخزين بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-28 11:31:31', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-28 11:31:31', 'status': 'نشط'}
جاري تعيين الصلاحيات...
جاري إنشاء StackedWidget...
تم إنشاء StackedWidget
جاري إنشاء زر وصفحة: المبيعات
تم إنشاء زر: المبيعات
جاري إنشاء صفحة: المبيعات
خطأ في معالجة المنتجات المفضلة: 'SalesView' object has no attribute 'product_buttons'
تم إنشاء صفحة: المبيعات
تم إضافة صفحة: المبيعات إلى StackedWidget
جاري إنشاء زر وصفحة: المنتجات
تم إنشاء زر: المنتجات
جاري إنشاء صفحة: المنتجات
تم إنشاء صفحة: المنتجات
تم إضافة صفحة: المنتجات إلى StackedWidget
جاري إنشاء زر وصفحة: الفواتير
تم إنشاء زر: الفواتير
جاري إنشاء صفحة: الفواتير
فلتر تاريخ البدء: 2025/05/28
فلتر تاريخ الانتهاء: 2025/05/28 (قبل 2025/05/29)
استعلام البحث: 
            SELECT i.id, i.reference_number, i.date, i.subtotal, i.tax, i.discount, 
                   i.total, i.paid_amount, i.remaining_amount, i.payment_method, i.status, 
                   i.notes, c.name as customer_name, c.id as customer_id
            FROM invoices i
            LEFT JOIN customers c ON i.customer_id = c.id
            WHERE 1=1
         AND (i.date >= ? OR i.date LIKE ?) AND i.date < ?
معلمات البحث: ['2025/05/28', '2025/05/28%', '2025/05/29']
تم العثور على 0 فاتورة
تم إنشاء صفحة: الفواتير
تم إضافة صفحة: الفواتير إلى StackedWidget
جاري إنشاء زر وصفحة: التقارير
تم إنشاء زر: التقارير
جاري إنشاء صفحة: التقارير
Invoices after initial filtering (status, customer): 0
تم إنشاء صفحة: التقارير
تم إضافة صفحة: التقارير إلى StackedWidget
جاري إنشاء زر وصفحة: العملاء
تم إنشاء زر: العملاء
جاري إنشاء صفحة: العملاء
تنسيق التاريخ: 2025/05/25 14:10:01
تم إنشاء صفحة: العملاء
تم إضافة صفحة: العملاء إلى StackedWidget
جاري إنشاء زر وصفحة: الموردين
تم إنشاء زر: الموردين
جاري إنشاء صفحة: الموردين
تم استرجاع 2 مورد من قاعدة البيانات
أول مورد: {'id': 10, 'name': 'احمد ربيع', 'phone': '', 'email': '', 'address': '', 'notes': '', 'contact_person': None, 'total_purchases': 5871.5, 'last_purchase_date': '2025/05/14 10:17:48', 'purchase_count': 1, 'total_debt': 2000.0}
تاريخ آخر عملية شراء للمورد احمد ربيع: 2025/05/14 10:17:48
تنسيق التاريخ: 2025/05/14 10:17:48
تاريخ آخر عملية شراء للمورد عمرو ابو رحمة: 2025/05/14 10:28:43
تنسيق التاريخ: 2025/05/14 10:28:43
تم إنشاء صفحة: الموردين
تم إضافة صفحة: الموردين إلى StackedWidget
جاري إنشاء زر وصفحة: المصروفات
تم إنشاء زر: المصروفات
جاري إنشاء صفحة: المصروفات
تم إنشاء صفحة: المصروفات
تم إضافة صفحة: المصروفات إلى StackedWidget
جاري إنشاء زر وصفحة: الإعدادات
تم إنشاء زر: الإعدادات
جاري إنشاء صفحة: الإعدادات
تم تحميل 2 مستخدم من قاعدة البيانات
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم إنشاء صفحة: الإعدادات
تم إضافة صفحة: الإعدادات إلى StackedWidget
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: جاري تحميل العملاء...
تم العثور على 0 عميل
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: عميل نقدي
تم تحديث صفحة المبيعات
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم قطع الاتصال بقاعدة البيانات
جاري تحديث معلومات المستخدم...
تم إنشاء النافذة الرئيسية بنجاح
تم إنشاء النافذة الرئيسية بنجاح
تم عرض النافذة الرئيسية
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
إجمالي الفاتورة: 85.0 - عدد المنتجات: 1
c:\Users\<USER>\Desktop\11\main.pyw:86: DeprecationWarning: sipPyTypeDict() is deprecated, the extension module should use sipPyTypeDictRef() instead
  class MainWindow(QMainWindow):
استخدام الخط المحمل من الملف: Readex Pro
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم إنشاء الجداول بنجاح
تم حفظ التغييرات في قاعدة البيانات
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
تم حفظ التغييرات في قاعدة البيانات
لا توجد بيانات في جدول purchase_invoices، لا حاجة للترحيل.
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
المستخدم الرئيسي موجود بالفعل
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
عرض نافذة تسجيل الدخول...
تم حفظ التغييرات في قاعدة البيانات
نتيجة تسجيل الدخول: 1
بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-28 11:33:13', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-28 11:33:13', 'status': 'نشط'}
جاري إنشاء النافذة الرئيسية...
بدء إنشاء النافذة الرئيسية...
تم استدعاء منشئ الفئة الأب
تم تخزين بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-28 11:33:13', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-28 11:33:13', 'status': 'نشط'}
جاري تعيين الصلاحيات...
جاري إنشاء StackedWidget...
تم إنشاء StackedWidget
جاري إنشاء زر وصفحة: المبيعات
تم إنشاء زر: المبيعات
جاري إنشاء صفحة: المبيعات
خطأ في معالجة المنتجات المفضلة: 'SalesView' object has no attribute 'product_buttons'
تم إنشاء صفحة: المبيعات
تم إضافة صفحة: المبيعات إلى StackedWidget
جاري إنشاء زر وصفحة: المنتجات
تم إنشاء زر: المنتجات
جاري إنشاء صفحة: المنتجات
تم إنشاء صفحة: المنتجات
تم إضافة صفحة: المنتجات إلى StackedWidget
جاري إنشاء زر وصفحة: الفواتير
تم إنشاء زر: الفواتير
جاري إنشاء صفحة: الفواتير
فلتر تاريخ البدء: 2025/05/28
فلتر تاريخ الانتهاء: 2025/05/28 (قبل 2025/05/29)
استعلام البحث: 
            SELECT i.id, i.reference_number, i.date, i.subtotal, i.tax, i.discount, 
                   i.total, i.paid_amount, i.remaining_amount, i.payment_method, i.status, 
                   i.notes, c.name as customer_name, c.id as customer_id
            FROM invoices i
            LEFT JOIN customers c ON i.customer_id = c.id
            WHERE 1=1
         AND (i.date >= ? OR i.date LIKE ?) AND i.date < ?
معلمات البحث: ['2025/05/28', '2025/05/28%', '2025/05/29']
تم العثور على 0 فاتورة
تم إنشاء صفحة: الفواتير
تم إضافة صفحة: الفواتير إلى StackedWidget
جاري إنشاء زر وصفحة: التقارير
تم إنشاء زر: التقارير
جاري إنشاء صفحة: التقارير
Invoices after initial filtering (status, customer): 0
تم إنشاء صفحة: التقارير
تم إضافة صفحة: التقارير إلى StackedWidget
جاري إنشاء زر وصفحة: العملاء
تم إنشاء زر: العملاء
جاري إنشاء صفحة: العملاء
تنسيق التاريخ: 2025/05/25 14:10:01
تم إنشاء صفحة: العملاء
تم إضافة صفحة: العملاء إلى StackedWidget
جاري إنشاء زر وصفحة: الموردين
تم إنشاء زر: الموردين
جاري إنشاء صفحة: الموردين
تم استرجاع 2 مورد من قاعدة البيانات
أول مورد: {'id': 10, 'name': 'احمد ربيع', 'phone': '', 'email': '', 'address': '', 'notes': '', 'contact_person': None, 'total_purchases': 5871.5, 'last_purchase_date': '2025/05/14 10:17:48', 'purchase_count': 1, 'total_debt': 2000.0}
تاريخ آخر عملية شراء للمورد احمد ربيع: 2025/05/14 10:17:48
تنسيق التاريخ: 2025/05/14 10:17:48
تاريخ آخر عملية شراء للمورد عمرو ابو رحمة: 2025/05/14 10:28:43
تنسيق التاريخ: 2025/05/14 10:28:43
تم إنشاء صفحة: الموردين
تم إضافة صفحة: الموردين إلى StackedWidget
جاري إنشاء زر وصفحة: المصروفات
تم إنشاء زر: المصروفات
جاري إنشاء صفحة: المصروفات
تم إنشاء صفحة: المصروفات
تم إضافة صفحة: المصروفات إلى StackedWidget
جاري إنشاء زر وصفحة: الإعدادات
تم إنشاء زر: الإعدادات
جاري إنشاء صفحة: الإعدادات
تم تحميل 2 مستخدم من قاعدة البيانات
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم إنشاء صفحة: الإعدادات
تم إضافة صفحة: الإعدادات إلى StackedWidget
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: جاري تحميل العملاء...
تم العثور على 0 عميل
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: عميل نقدي
تم تحديث صفحة المبيعات
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم قطع الاتصال بقاعدة البيانات
جاري تحديث معلومات المستخدم...
تم إنشاء النافذة الرئيسية بنجاح
تم إنشاء النافذة الرئيسية بنجاح
تم عرض النافذة الرئيسية
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
إجمالي الفاتورة: 450.0 - عدد المنتجات: 1
c:\Users\<USER>\Desktop\11\main.pyw:86: DeprecationWarning: sipPyTypeDict() is deprecated, the extension module should use sipPyTypeDictRef() instead
  class MainWindow(QMainWindow):
استخدام الخط المحمل من الملف: Readex Pro
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم إنشاء الجداول بنجاح
تم حفظ التغييرات في قاعدة البيانات
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
تم حفظ التغييرات في قاعدة البيانات
لا توجد بيانات في جدول purchase_invoices، لا حاجة للترحيل.
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
المستخدم الرئيسي موجود بالفعل
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
عرض نافذة تسجيل الدخول...
تم حفظ التغييرات في قاعدة البيانات
نتيجة تسجيل الدخول: 1
بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-28 11:35:32', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-28 11:35:32', 'status': 'نشط'}
جاري إنشاء النافذة الرئيسية...
بدء إنشاء النافذة الرئيسية...
تم استدعاء منشئ الفئة الأب
تم تخزين بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-28 11:35:32', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-28 11:35:32', 'status': 'نشط'}
جاري تعيين الصلاحيات...
جاري إنشاء StackedWidget...
تم إنشاء StackedWidget
جاري إنشاء زر وصفحة: المبيعات
تم إنشاء زر: المبيعات
جاري إنشاء صفحة: المبيعات
خطأ في معالجة المنتجات المفضلة: 'SalesView' object has no attribute 'product_buttons'
تم إنشاء صفحة: المبيعات
تم إضافة صفحة: المبيعات إلى StackedWidget
جاري إنشاء زر وصفحة: المنتجات
تم إنشاء زر: المنتجات
جاري إنشاء صفحة: المنتجات
تم إنشاء صفحة: المنتجات
تم إضافة صفحة: المنتجات إلى StackedWidget
جاري إنشاء زر وصفحة: الفواتير
تم إنشاء زر: الفواتير
جاري إنشاء صفحة: الفواتير
فلتر تاريخ البدء: 2025/05/28
فلتر تاريخ الانتهاء: 2025/05/28 (قبل 2025/05/29)
استعلام البحث: 
            SELECT i.id, i.reference_number, i.date, i.subtotal, i.tax, i.discount, 
                   i.total, i.paid_amount, i.remaining_amount, i.payment_method, i.status, 
                   i.notes, c.name as customer_name, c.id as customer_id
            FROM invoices i
            LEFT JOIN customers c ON i.customer_id = c.id
            WHERE 1=1
         AND (i.date >= ? OR i.date LIKE ?) AND i.date < ?
معلمات البحث: ['2025/05/28', '2025/05/28%', '2025/05/29']
تم العثور على 0 فاتورة
تم إنشاء صفحة: الفواتير
تم إضافة صفحة: الفواتير إلى StackedWidget
جاري إنشاء زر وصفحة: التقارير
تم إنشاء زر: التقارير
جاري إنشاء صفحة: التقارير
Invoices after initial filtering (status, customer): 0
تم إنشاء صفحة: التقارير
تم إضافة صفحة: التقارير إلى StackedWidget
جاري إنشاء زر وصفحة: العملاء
تم إنشاء زر: العملاء
جاري إنشاء صفحة: العملاء
تنسيق التاريخ: 2025/05/25 14:10:01
تم إنشاء صفحة: العملاء
تم إضافة صفحة: العملاء إلى StackedWidget
جاري إنشاء زر وصفحة: الموردين
تم إنشاء زر: الموردين
جاري إنشاء صفحة: الموردين
تم استرجاع 2 مورد من قاعدة البيانات
أول مورد: {'id': 10, 'name': 'احمد ربيع', 'phone': '', 'email': '', 'address': '', 'notes': '', 'contact_person': None, 'total_purchases': 5871.5, 'last_purchase_date': '2025/05/14 10:17:48', 'purchase_count': 1, 'total_debt': 2000.0}
تاريخ آخر عملية شراء للمورد احمد ربيع: 2025/05/14 10:17:48
تنسيق التاريخ: 2025/05/14 10:17:48
تاريخ آخر عملية شراء للمورد عمرو ابو رحمة: 2025/05/14 10:28:43
تنسيق التاريخ: 2025/05/14 10:28:43
تم إنشاء صفحة: الموردين
تم إضافة صفحة: الموردين إلى StackedWidget
جاري إنشاء زر وصفحة: المصروفات
تم إنشاء زر: المصروفات
جاري إنشاء صفحة: المصروفات
تم إنشاء صفحة: المصروفات
تم إضافة صفحة: المصروفات إلى StackedWidget
جاري إنشاء زر وصفحة: الإعدادات
تم إنشاء زر: الإعدادات
جاري إنشاء صفحة: الإعدادات
تم تحميل 2 مستخدم من قاعدة البيانات
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم إنشاء صفحة: الإعدادات
تم إضافة صفحة: الإعدادات إلى StackedWidget
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: جاري تحميل العملاء...
تم العثور على 0 عميل
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: عميل نقدي
تم تحديث صفحة المبيعات
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم قطع الاتصال بقاعدة البيانات
جاري تحديث معلومات المستخدم...
تم إنشاء النافذة الرئيسية بنجاح
تم إنشاء النافذة الرئيسية بنجاح
تم عرض النافذة الرئيسية
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
إجمالي الفاتورة: 140.0 - عدد المنتجات: 1
تم تحديث صفحة المنتجات
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم حفظ التغييرات في قاعدة البيانات
تم استرجاع 2 مورد من قاعدة البيانات
أول مورد: {'id': 10, 'name': 'احمد ربيع', 'phone': '', 'email': '', 'address': '', 'notes': '', 'contact_person': None, 'total_purchases': 5871.5, 'last_purchase_date': '2025/05/14 10:17:48', 'purchase_count': 1, 'total_debt': 2000.0}
تاريخ آخر عملية شراء للمورد احمد ربيع: 2025/05/14 10:17:48
تنسيق التاريخ: 2025/05/14 10:17:48
تاريخ آخر عملية شراء للمورد عمرو ابو رحمة: 2025/05/14 10:28:43
تنسيق التاريخ: 2025/05/14 10:28:43
تم تحديث صفحة الموردين
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
[DEBUG] Obteniendo compras para el proveedor ID: 11
[DEBUG] ID de proveedor validado como entero: 11
[DEBUG] Compras encontradas con SupplierModel.get_supplier_purchases: 1
[DEBUG] Primera compra encontrada: {'id': 9, 'reference_number': 'PO-2025-05-14-102843', 'purchase_date': '2025/05/14 10:28:43', 'total_amount': 1820.0, 'paid_amount': 1000.0, 'remaining_amount': 820.0, 'payment_status': 'غير مدفوعة', 'notes': '', 'supplier_name': 'عمرو ابو رحمة', 'item_count': 4}
[DEBUG] تحديث المشتريات للمورد ذو المعرف: 11
[DEBUG] تم العثور على 2 فاتورة في النظام
[DEBUG] معرفات الموردين المتوفرة في الفواتير: {'10', '11'}
[DEBUG] معرف المورد الذي نبحث عنه: [11, '11', 11]
[DEBUG] تمت إضافة الفاتورة رقم 9 للقائمة (supplier_id=11)
[DEBUG] تم العثور على 1 فاتورة لهذا المورد
[DEBUG] تم تحديث سجل المشتريات. تم العثور على 1 عملية شراء.
[DEBUG] Obteniendo compras para el proveedor ID: 10
[DEBUG] ID de proveedor validado como entero: 10
[DEBUG] Compras encontradas con SupplierModel.get_supplier_purchases: 1
[DEBUG] Primera compra encontrada: {'id': 8, 'reference_number': 'PO-2025-05-14-101748', 'purchase_date': '2025/05/14 10:17:48', 'total_amount': 5871.5, 'paid_amount': 3871.5, 'remaining_amount': 2000.0, 'payment_status': 'غير مدفوعة', 'notes': '', 'supplier_name': 'احمد ربيع', 'item_count': 14}
[DEBUG] تحديث المشتريات للمورد ذو المعرف: 10
[DEBUG] تم العثور على 2 فاتورة في النظام
[DEBUG] معرفات الموردين المتوفرة في الفواتير: {'10', '11'}
[DEBUG] معرف المورد الذي نبحث عنه: [10, '10', 10]
[DEBUG] تمت إضافة الفاتورة رقم 8 للقائمة (supplier_id=10)
[DEBUG] تم العثور على 1 فاتورة لهذا المورد
[DEBUG] تم تحديث سجل المشتريات. تم العثور على 1 عملية شراء.
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم حفظ التغييرات في قاعدة البيانات
تم تحديث صفحة الإعدادات
تم قطع الاتصال بقاعدة البيانات
تم تحديث محتوى تاب المستخدمين
تم تحديث محتوى تاب قاعدة البيانات والنسخ الاحتياطي
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم استرجاع 2 مورد من قاعدة البيانات
أول مورد: {'id': 10, 'name': 'احمد ربيع', 'phone': '', 'email': '', 'address': '', 'notes': '', 'contact_person': None, 'total_purchases': 5871.5, 'last_purchase_date': '2025/05/14 10:17:48', 'purchase_count': 1, 'total_debt': 2000.0}
تاريخ آخر عملية شراء للمورد احمد ربيع: 2025/05/14 10:17:48
تنسيق التاريخ: 2025/05/14 10:17:48
تاريخ آخر عملية شراء للمورد عمرو ابو رحمة: 2025/05/14 10:28:43
تنسيق التاريخ: 2025/05/14 10:28:43
تم تحديث صفحة الموردين
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تنسيق التاريخ: 2025/05/25 14:10:01
تم تحديث صفحة العملاء
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
Invoices after initial filtering (status, customer): 0
تم تحديث صفحة التقارير
تم قطع الاتصال بقاعدة البيانات
فلتر تاريخ البدء: 2025/05/28
فلتر تاريخ الانتهاء: 2025/05/28 (قبل 2025/05/29)
استعلام البحث: 
            SELECT i.id, i.reference_number, i.date, i.subtotal, i.tax, i.discount, 
                   i.total, i.paid_amount, i.remaining_amount, i.payment_method, i.status, 
                   i.notes, c.name as customer_name, c.id as customer_id
            FROM invoices i
            LEFT JOIN customers c ON i.customer_id = c.id
            WHERE 1=1
         AND (i.date >= ? OR i.date LIKE ?) AND i.date < ?
معلمات البحث: ['2025/05/28', '2025/05/28%', '2025/05/29']
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم العثور على 0 فاتورة
تم تحديث صفحة الفواتير
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم تحديث صفحة المنتجات
تم قطع الاتصال بقاعدة البيانات
تم اختيار العميل: 
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم العثور على 1 عميل
تم اختيار العميل: جاري تحميل العملاء...
تم العثور على 0 عميل
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: عميل نقدي
تم تحديث صفحة المبيعات
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
إجمالي الفاتورة: 225.0 - عدد المنتجات: 2
C:\Users\<USER>\Desktop\11\main.pyw:86: DeprecationWarning: sipPyTypeDict() is deprecated, the extension module should use sipPyTypeDictRef() instead
  class MainWindow(QMainWindow):
استخدام الخط المحمل من الملف: Readex Pro
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم إنشاء الجداول بنجاح
تم حفظ التغييرات في قاعدة البيانات
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
تم حفظ التغييرات في قاعدة البيانات
لا توجد بيانات في جدول purchase_invoices، لا حاجة للترحيل.
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
المستخدم الرئيسي موجود بالفعل
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
عرض نافذة تسجيل الدخول...
تم حفظ التغييرات في قاعدة البيانات
نتيجة تسجيل الدخول: 1
بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-28 11:51:45', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-28 11:51:45', 'status': 'نشط'}
جاري إنشاء النافذة الرئيسية...
بدء إنشاء النافذة الرئيسية...
تم استدعاء منشئ الفئة الأب
تم تخزين بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-28 11:51:45', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-28 11:51:45', 'status': 'نشط'}
جاري تعيين الصلاحيات...
جاري إنشاء StackedWidget...
تم إنشاء StackedWidget
جاري إنشاء زر وصفحة: المبيعات
تم إنشاء زر: المبيعات
جاري إنشاء صفحة: المبيعات
خطأ في معالجة المنتجات المفضلة: 'SalesView' object has no attribute 'product_buttons'
تم إنشاء صفحة: المبيعات
تم إضافة صفحة: المبيعات إلى StackedWidget
جاري إنشاء زر وصفحة: المنتجات
تم إنشاء زر: المنتجات
جاري إنشاء صفحة: المنتجات
تم إنشاء صفحة: المنتجات
تم إضافة صفحة: المنتجات إلى StackedWidget
جاري إنشاء زر وصفحة: الفواتير
تم إنشاء زر: الفواتير
جاري إنشاء صفحة: الفواتير
فلتر تاريخ البدء: 2025/05/28
فلتر تاريخ الانتهاء: 2025/05/28 (قبل 2025/05/29)
استعلام البحث: 
            SELECT i.id, i.reference_number, i.date, i.subtotal, i.tax, i.discount, 
                   i.total, i.paid_amount, i.remaining_amount, i.payment_method, i.status, 
                   i.notes, c.name as customer_name, c.id as customer_id
            FROM invoices i
            LEFT JOIN customers c ON i.customer_id = c.id
            WHERE 1=1
         AND (i.date >= ? OR i.date LIKE ?) AND i.date < ?
معلمات البحث: ['2025/05/28', '2025/05/28%', '2025/05/29']
تم العثور على 0 فاتورة
تم إنشاء صفحة: الفواتير
تم إضافة صفحة: الفواتير إلى StackedWidget
جاري إنشاء زر وصفحة: التقارير
تم إنشاء زر: التقارير
جاري إنشاء صفحة: التقارير
Invoices after initial filtering (status, customer): 0
تم إنشاء صفحة: التقارير
تم إضافة صفحة: التقارير إلى StackedWidget
جاري إنشاء زر وصفحة: العملاء
تم إنشاء زر: العملاء
جاري إنشاء صفحة: العملاء
تنسيق التاريخ: 2025/05/25 14:10:01
تم إنشاء صفحة: العملاء
تم إضافة صفحة: العملاء إلى StackedWidget
جاري إنشاء زر وصفحة: الموردين
تم إنشاء زر: الموردين
جاري إنشاء صفحة: الموردين
تم استرجاع 2 مورد من قاعدة البيانات
أول مورد: {'id': 10, 'name': 'احمد ربيع', 'phone': '', 'email': '', 'address': '', 'notes': '', 'contact_person': None, 'total_purchases': 5871.5, 'last_purchase_date': '2025/05/14 10:17:48', 'purchase_count': 1, 'total_debt': 2000.0}
تاريخ آخر عملية شراء للمورد احمد ربيع: 2025/05/14 10:17:48
تنسيق التاريخ: 2025/05/14 10:17:48
تاريخ آخر عملية شراء للمورد عمرو ابو رحمة: 2025/05/14 10:28:43
تنسيق التاريخ: 2025/05/14 10:28:43
تم إنشاء صفحة: الموردين
تم إضافة صفحة: الموردين إلى StackedWidget
جاري إنشاء زر وصفحة: المصروفات
تم إنشاء زر: المصروفات
جاري إنشاء صفحة: المصروفات
تم إنشاء صفحة: المصروفات
تم إضافة صفحة: المصروفات إلى StackedWidget
جاري إنشاء زر وصفحة: الإعدادات
تم إنشاء زر: الإعدادات
جاري إنشاء صفحة: الإعدادات
تم تحميل 2 مستخدم من قاعدة البيانات
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم إنشاء صفحة: الإعدادات
تم إضافة صفحة: الإعدادات إلى StackedWidget
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: جاري تحميل العملاء...
تم العثور على 0 عميل
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: عميل نقدي
تم تحديث صفحة المبيعات
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم قطع الاتصال بقاعدة البيانات
جاري تحديث معلومات المستخدم...
تم إنشاء النافذة الرئيسية بنجاح
تم إنشاء النافذة الرئيسية بنجاح
تم عرض النافذة الرئيسية
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
إجمالي الفاتورة: 160.0 - عدد المنتجات: 1
C:\Users\<USER>\Desktop\11\main.pyw:86: DeprecationWarning: sipPyTypeDict() is deprecated, the extension module should use sipPyTypeDictRef() instead
  class MainWindow(QMainWindow):
استخدام الخط المحمل من الملف: Readex Pro
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم إنشاء الجداول بنجاح
تم حفظ التغييرات في قاعدة البيانات
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
تم حفظ التغييرات في قاعدة البيانات
لا توجد بيانات في جدول purchase_invoices، لا حاجة للترحيل.
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
المستخدم الرئيسي موجود بالفعل
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
عرض نافذة تسجيل الدخول...
تم حفظ التغييرات في قاعدة البيانات
نتيجة تسجيل الدخول: 1
بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-28 12:02:43', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-28 12:02:43', 'status': 'نشط'}
جاري إنشاء النافذة الرئيسية...
بدء إنشاء النافذة الرئيسية...
تم استدعاء منشئ الفئة الأب
تم تخزين بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-28 12:02:43', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-28 12:02:43', 'status': 'نشط'}
جاري تعيين الصلاحيات...
جاري إنشاء StackedWidget...
تم إنشاء StackedWidget
جاري إنشاء زر وصفحة: المبيعات
تم إنشاء زر: المبيعات
جاري إنشاء صفحة: المبيعات
خطأ في معالجة المنتجات المفضلة: 'SalesView' object has no attribute 'product_buttons'
تم إنشاء صفحة: المبيعات
تم إضافة صفحة: المبيعات إلى StackedWidget
جاري إنشاء زر وصفحة: المنتجات
تم إنشاء زر: المنتجات
جاري إنشاء صفحة: المنتجات
تم إنشاء صفحة: المنتجات
تم إضافة صفحة: المنتجات إلى StackedWidget
جاري إنشاء زر وصفحة: الفواتير
تم إنشاء زر: الفواتير
جاري إنشاء صفحة: الفواتير
فلتر تاريخ البدء: 2025/05/28
فلتر تاريخ الانتهاء: 2025/05/28 (قبل 2025/05/29)
استعلام البحث: 
            SELECT i.id, i.reference_number, i.date, i.subtotal, i.tax, i.discount, 
                   i.total, i.paid_amount, i.remaining_amount, i.payment_method, i.status, 
                   i.notes, c.name as customer_name, c.id as customer_id
            FROM invoices i
            LEFT JOIN customers c ON i.customer_id = c.id
            WHERE 1=1
         AND (i.date >= ? OR i.date LIKE ?) AND i.date < ?
معلمات البحث: ['2025/05/28', '2025/05/28%', '2025/05/29']
تم العثور على 0 فاتورة
تم إنشاء صفحة: الفواتير
تم إضافة صفحة: الفواتير إلى StackedWidget
جاري إنشاء زر وصفحة: التقارير
تم إنشاء زر: التقارير
جاري إنشاء صفحة: التقارير
Invoices after initial filtering (status, customer): 0
تم إنشاء صفحة: التقارير
تم إضافة صفحة: التقارير إلى StackedWidget
جاري إنشاء زر وصفحة: العملاء
تم إنشاء زر: العملاء
جاري إنشاء صفحة: العملاء
تنسيق التاريخ: 2025/05/25 14:10:01
تم إنشاء صفحة: العملاء
تم إضافة صفحة: العملاء إلى StackedWidget
جاري إنشاء زر وصفحة: الموردين
تم إنشاء زر: الموردين
جاري إنشاء صفحة: الموردين
تم استرجاع 2 مورد من قاعدة البيانات
أول مورد: {'id': 10, 'name': 'احمد ربيع', 'phone': '', 'email': '', 'address': '', 'notes': '', 'contact_person': None, 'total_purchases': 5871.5, 'last_purchase_date': '2025/05/14 10:17:48', 'purchase_count': 1, 'total_debt': 2000.0}
تاريخ آخر عملية شراء للمورد احمد ربيع: 2025/05/14 10:17:48
تنسيق التاريخ: 2025/05/14 10:17:48
تاريخ آخر عملية شراء للمورد عمرو ابو رحمة: 2025/05/14 10:28:43
تنسيق التاريخ: 2025/05/14 10:28:43
تم إنشاء صفحة: الموردين
تم إضافة صفحة: الموردين إلى StackedWidget
جاري إنشاء زر وصفحة: المصروفات
تم إنشاء زر: المصروفات
جاري إنشاء صفحة: المصروفات
تم إنشاء صفحة: المصروفات
تم إضافة صفحة: المصروفات إلى StackedWidget
جاري إنشاء زر وصفحة: الإعدادات
تم إنشاء زر: الإعدادات
جاري إنشاء صفحة: الإعدادات
تم تحميل 2 مستخدم من قاعدة البيانات
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم إنشاء صفحة: الإعدادات
تم إضافة صفحة: الإعدادات إلى StackedWidget
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: جاري تحميل العملاء...
تم العثور على 0 عميل
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: عميل نقدي
تم تحديث صفحة المبيعات
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم قطع الاتصال بقاعدة البيانات
جاري تحديث معلومات المستخدم...
تم إنشاء النافذة الرئيسية بنجاح
تم إنشاء النافذة الرئيسية بنجاح
تم عرض النافذة الرئيسية
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم استرجاع 2 مورد من قاعدة البيانات
أول مورد: {'id': 10, 'name': 'احمد ربيع', 'phone': '', 'email': '', 'address': '', 'notes': '', 'contact_person': None, 'total_purchases': 5871.5, 'last_purchase_date': '2025/05/14 10:17:48', 'purchase_count': 1, 'total_debt': 2000.0}
تاريخ آخر عملية شراء للمورد احمد ربيع: 2025/05/14 10:17:48
تنسيق التاريخ: 2025/05/14 10:17:48
تاريخ آخر عملية شراء للمورد عمرو ابو رحمة: 2025/05/14 10:28:43
تنسيق التاريخ: 2025/05/14 10:28:43
تم تحديث صفحة الموردين
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
[DEBUG] Obteniendo compras para el proveedor ID: 10
[DEBUG] ID de proveedor validado como entero: 10
[DEBUG] Compras encontradas con SupplierModel.get_supplier_purchases: 1
[DEBUG] Primera compra encontrada: {'id': 8, 'reference_number': 'PO-2025-05-14-101748', 'purchase_date': '2025/05/14 10:17:48', 'total_amount': 5871.5, 'paid_amount': 3871.5, 'remaining_amount': 2000.0, 'payment_status': 'غير مدفوعة', 'notes': '', 'supplier_name': 'احمد ربيع', 'item_count': 14}
[DEBUG] تحديث المشتريات للمورد ذو المعرف: 10
[DEBUG] تم العثور على 2 فاتورة في النظام
[DEBUG] معرفات الموردين المتوفرة في الفواتير: {'11', '10'}
[DEBUG] معرف المورد الذي نبحث عنه: [10, '10', 10]
[DEBUG] تمت إضافة الفاتورة رقم 8 للقائمة (supplier_id=10)
[DEBUG] تم العثور على 1 فاتورة لهذا المورد
[DEBUG] تم تحديث سجل المشتريات. تم العثور على 1 عملية شراء.
تم حفظ التغييرات في قاعدة البيانات
[DEBUG] تحديث المشتريات للمورد ذو المعرف: 10
[DEBUG] تم العثور على 2 فاتورة في النظام
[DEBUG] معرفات الموردين المتوفرة في الفواتير: {'11', '10'}
[DEBUG] معرف المورد الذي نبحث عنه: [10, '10', 10]
[DEBUG] تمت إضافة الفاتورة رقم 8 للقائمة (supplier_id=10)
[DEBUG] تم العثور على 1 فاتورة لهذا المورد
[DEBUG] تم تحديث سجل المشتريات. تم العثور على 1 عملية شراء.
[DEBUG] Obteniendo compras para el proveedor ID: 10
[DEBUG] ID de proveedor validado como entero: 10
[DEBUG] Compras encontradas con SupplierModel.get_supplier_purchases: 1
[DEBUG] Primera compra encontrada: {'id': 8, 'reference_number': 'PO-2025-05-14-101748', 'purchase_date': '2025/05/14 10:17:48', 'total_amount': 5871.5, 'paid_amount': 4371.5, 'remaining_amount': 1500.0, 'payment_status': 'غير مدفوعة', 'notes': '', 'supplier_name': 'احمد ربيع', 'item_count': 14}
[DEBUG] تحديث المشتريات للمورد ذو المعرف: 10
[DEBUG] تم العثور على 2 فاتورة في النظام
[DEBUG] معرفات الموردين المتوفرة في الفواتير: {'11', '10'}
[DEBUG] معرف المورد الذي نبحث عنه: [10, '10', 10]
[DEBUG] تمت إضافة الفاتورة رقم 8 للقائمة (supplier_id=10)
[DEBUG] تم العثور على 1 فاتورة لهذا المورد
[DEBUG] تم تحديث سجل المشتريات. تم العثور على 1 عملية شراء.
[DEBUG] عرض تفاصيل الفاتورة رقم 8
[DEBUG] عدد العناصر المستلمة: 14
[DEBUG] العنصر 1: None - الكمية: 5 - الإجمالي: 470.0
[DEBUG] العنصر 2: None - الكمية: 10 - الإجمالي: 710.0
[DEBUG] العنصر 3: None - الكمية: 10 - الإجمالي: 270.0
[DEBUG] العنصر 4: None - الكمية: 3 - الإجمالي: 720.0
[DEBUG] العنصر 5: None - الكمية: 3 - الإجمالي: 690.0
[DEBUG] العنصر 6: None - الكمية: 10 - الإجمالي: 230.0
[DEBUG] العنصر 7: None - الكمية: 1 - الإجمالي: 105.0
[DEBUG] العنصر 8: None - الكمية: 1 - الإجمالي: 115.0
[DEBUG] العنصر 9: None - الكمية: 4 - الإجمالي: 248.0
[DEBUG] العنصر 10: None - الكمية: 4 - الإجمالي: 288.0
[DEBUG] العنصر 11: None - الكمية: 2 - الإجمالي: 54.0
[DEBUG] العنصر 12: None - الكمية: 3 - الإجمالي: 129.0
[DEBUG] العنصر 13: None - الكمية: 115 - الإجمالي: 1552.5
[DEBUG] العنصر 14: None - الكمية: 10 - الإجمالي: 290.0
[DEBUG] ملء جدول العناصر بـ 14 عنصر
[DEBUG] تمت إضافة العنصر 1: شاحن ستارت سوبر فوك ميكرو, الكمية: 5, الإجمالي: 470.00
[DEBUG] تمت إضافة العنصر 2: شاحن ستارت دورار ميكرو, الكمية: 10, الإجمالي: 710.00
[DEBUG] تمت إضافة العنصر 3: كبل ستارت 120w ميكرو, الكمية: 10, الإجمالي: 270.00
[DEBUG] تمت إضافة العنصر 4: ايربودز برو 2, الكمية: 3, الإجمالي: 720.00
[DEBUG] تمت إضافة العنصر 5: ايربودز برو 3, الكمية: 3, الإجمالي: 690.00
[DEBUG] تمت إضافة العنصر 6: بطارية نوكيا, الكمية: 10, الإجمالي: 230.00
[DEBUG] تمت إضافة العنصر 7: فلاشة 16G, الكمية: 1, الإجمالي: 105.00
[DEBUG] تمت إضافة العنصر 8: فلاشة 32G, الكمية: 1, الإجمالي: 115.00
[DEBUG] تمت إضافة العنصر 9: ميموري 4G, الكمية: 4, الإجمالي: 248.00
[DEBUG] تمت إضافة العنصر 10: ميموري 8G, الكمية: 4, الإجمالي: 288.00
[DEBUG] تمت إضافة العنصر 11: استيك سيلكون, الكمية: 2, الإجمالي: 54.00
[DEBUG] تمت إضافة العنصر 12: استيك قماش, الكمية: 3, الإجمالي: 129.00
[DEBUG] تمت إضافة العنصر 13: اسكرينة HD ULTRA, الكمية: 115, الإجمالي: 1552.50
[DEBUG] تمت إضافة العنصر 14: كبل ستارت 120W تايب سي, الكمية: 10, الإجمالي: 290.00
c:\Users\<USER>\Desktop\11\main.pyw:86: DeprecationWarning: sipPyTypeDict() is deprecated, the extension module should use sipPyTypeDictRef() instead
  class MainWindow(QMainWindow):
استخدام الخط المحمل من الملف: Readex Pro
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم إنشاء الجداول بنجاح
تم حفظ التغييرات في قاعدة البيانات
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
تم حفظ التغييرات في قاعدة البيانات
لا توجد بيانات في جدول purchase_invoices، لا حاجة للترحيل.
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
المستخدم الرئيسي موجود بالفعل
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
عرض نافذة تسجيل الدخول...
نتيجة تسجيل الدخول: 0
بيانات المستخدم: None
فشل تسجيل الدخول أو تم الإلغاء
c:\Users\<USER>\Desktop\11\main.pyw:86: DeprecationWarning: sipPyTypeDict() is deprecated, the extension module should use sipPyTypeDictRef() instead
  class MainWindow(QMainWindow):
استخدام الخط المحمل من الملف: Readex Pro
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم إنشاء الجداول بنجاح
تم حفظ التغييرات في قاعدة البيانات
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
تم حفظ التغييرات في قاعدة البيانات
لا توجد بيانات في جدول purchase_invoices، لا حاجة للترحيل.
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
المستخدم الرئيسي موجود بالفعل
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
عرض نافذة تسجيل الدخول...
تم حفظ التغييرات في قاعدة البيانات
نتيجة تسجيل الدخول: 1
بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-28 15:25:52', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-28 15:25:52', 'status': 'نشط'}
جاري إنشاء النافذة الرئيسية...
بدء إنشاء النافذة الرئيسية...
تم استدعاء منشئ الفئة الأب
تم تخزين بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-28 15:25:52', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-28 15:25:52', 'status': 'نشط'}
جاري تعيين الصلاحيات...
جاري إنشاء StackedWidget...
تم إنشاء StackedWidget
جاري إنشاء زر وصفحة: المبيعات
تم إنشاء زر: المبيعات
جاري إنشاء صفحة: المبيعات
خطأ في معالجة المنتجات المفضلة: 'SalesView' object has no attribute 'product_buttons'
تم إنشاء صفحة: المبيعات
تم إضافة صفحة: المبيعات إلى StackedWidget
جاري إنشاء زر وصفحة: المنتجات
تم إنشاء زر: المنتجات
جاري إنشاء صفحة: المنتجات
تم إنشاء صفحة: المنتجات
تم إضافة صفحة: المنتجات إلى StackedWidget
جاري إنشاء زر وصفحة: الفواتير
تم إنشاء زر: الفواتير
جاري إنشاء صفحة: الفواتير
فلتر تاريخ البدء: 2025/05/28
فلتر تاريخ الانتهاء: 2025/05/28 (قبل 2025/05/29)
استعلام البحث: 
            SELECT i.id, i.reference_number, i.date, i.subtotal, i.tax, i.discount, 
                   i.total, i.paid_amount, i.remaining_amount, i.payment_method, i.status, 
                   i.notes, c.name as customer_name, c.id as customer_id
            FROM invoices i
            LEFT JOIN customers c ON i.customer_id = c.id
            WHERE 1=1
         AND (i.date >= ? OR i.date LIKE ?) AND i.date < ?
معلمات البحث: ['2025/05/28', '2025/05/28%', '2025/05/29']
تم العثور على 0 فاتورة
تم إنشاء صفحة: الفواتير
تم إضافة صفحة: الفواتير إلى StackedWidget
جاري إنشاء زر وصفحة: التقارير
تم إنشاء زر: التقارير
جاري إنشاء صفحة: التقارير
Invoices after initial filtering (status, customer): 0
تم إنشاء صفحة: التقارير
تم إضافة صفحة: التقارير إلى StackedWidget
جاري إنشاء زر وصفحة: العملاء
تم إنشاء زر: العملاء
جاري إنشاء صفحة: العملاء
تنسيق التاريخ: 2025/05/25 14:10:01
تم إنشاء صفحة: العملاء
تم إضافة صفحة: العملاء إلى StackedWidget
جاري إنشاء زر وصفحة: الموردين
تم إنشاء زر: الموردين
جاري إنشاء صفحة: الموردين
تم استرجاع 2 مورد من قاعدة البيانات
أول مورد: {'id': 10, 'name': 'احمد ربيع', 'phone': '', 'email': '', 'address': '', 'notes': '', 'contact_person': None, 'total_purchases': 5871.5, 'last_purchase_date': '2025/05/14 10:17:48', 'purchase_count': 1, 'total_debt': 1500.0}
تاريخ آخر عملية شراء للمورد احمد ربيع: 2025/05/14 10:17:48
تنسيق التاريخ: 2025/05/14 10:17:48
تاريخ آخر عملية شراء للمورد عمرو ابو رحمة: 2025/05/14 10:28:43
تنسيق التاريخ: 2025/05/14 10:28:43
تم إنشاء صفحة: الموردين
تم إضافة صفحة: الموردين إلى StackedWidget
جاري إنشاء زر وصفحة: المصروفات
تم إنشاء زر: المصروفات
جاري إنشاء صفحة: المصروفات
تم إنشاء صفحة: المصروفات
تم إضافة صفحة: المصروفات إلى StackedWidget
جاري إنشاء زر وصفحة: الإعدادات
تم إنشاء زر: الإعدادات
جاري إنشاء صفحة: الإعدادات
تم تحميل 2 مستخدم من قاعدة البيانات
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم إنشاء صفحة: الإعدادات
تم إضافة صفحة: الإعدادات إلى StackedWidget
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: جاري تحميل العملاء...
تم العثور على 0 عميل
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: عميل نقدي
تم تحديث صفحة المبيعات
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم قطع الاتصال بقاعدة البيانات
جاري تحديث معلومات المستخدم...
تم إنشاء النافذة الرئيسية بنجاح
تم إنشاء النافذة الرئيسية بنجاح
تم عرض النافذة الرئيسية
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
إجمالي الفاتورة: 160.0 - عدد المنتجات: 1
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\11\main.pyw", line 77, in <module>
    from views.sales import SalesView
  File "c:\Users\<USER>\Desktop\11\views\sales.py", line 3052
    if new_quantity > available_quantity:
    ^^
IndentationError: expected an indented block after 'if' statement on line 3050
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\11\main.pyw", line 77, in <module>
    from views.sales import SalesView
  File "c:\Users\<USER>\Desktop\11\views\sales.py", line 3052
    if new_quantity > available_quantity:
    ^^
IndentationError: expected an indented block after 'if' statement on line 3050
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\11\main.pyw", line 77, in <module>
    from views.sales import SalesView
  File "C:\Users\<USER>\Desktop\11\views\sales.py", line 3052
    if new_quantity > available_quantity:
    ^^
IndentationError: expected an indented block after 'if' statement on line 3050
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\11\main.pyw", line 77, in <module>
    from views.sales import SalesView
  File "C:\Users\<USER>\Desktop\11\views\sales.py", line 3052
    if new_quantity > available_quantity:
    ^^
IndentationError: expected an indented block after 'if' statement on line 3050
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\11\main.pyw", line 77, in <module>
    from views.sales import SalesView
  File "C:\Users\<USER>\Desktop\11\views\sales.py", line 3052
    if new_quantity > available_quantity:
    ^^
IndentationError: expected an indented block after 'if' statement on line 3050
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\11\main.pyw", line 77, in <module>
    from views.sales import SalesView
  File "C:\Users\<USER>\Desktop\11\views\sales.py", line 3052
    if new_quantity > available_quantity:
    ^^
IndentationError: expected an indented block after 'if' statement on line 3050
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\11\main.pyw", line 77, in <module>
    from views.sales import SalesView
  File "C:\Users\<USER>\Desktop\11\views\sales.py", line 3052
    if new_quantity > available_quantity:
    ^^
IndentationError: expected an indented block after 'if' statement on line 3050
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\11\main.pyw", line 74, in <module>
    from views.sales import SalesView
  File "C:\Users\<USER>\Desktop\11\views\sales.py", line 3052
    if new_quantity > available_quantity:
    ^^
IndentationError: expected an indented block after 'if' statement on line 3050
c:\Users\<USER>\Desktop\11\main.pyw:86: DeprecationWarning: sipPyTypeDict() is deprecated, the extension module should use sipPyTypeDictRef() instead
  class MainWindow(QMainWindow):
استخدام الخط المحمل من الملف: Readex Pro
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم إنشاء الجداول بنجاح
تم حفظ التغييرات في قاعدة البيانات
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
تم حفظ التغييرات في قاعدة البيانات
لا توجد بيانات في جدول purchase_invoices، لا حاجة للترحيل.
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
المستخدم الرئيسي موجود بالفعل
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
عرض نافذة تسجيل الدخول...
نتيجة تسجيل الدخول: 0
بيانات المستخدم: None
فشل تسجيل الدخول أو تم الإلغاء
c:\Users\<USER>\Desktop\11\main.pyw:86: DeprecationWarning: sipPyTypeDict() is deprecated, the extension module should use sipPyTypeDictRef() instead
  class MainWindow(QMainWindow):
استخدام الخط المحمل من الملف: Readex Pro
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم إنشاء الجداول بنجاح
تم حفظ التغييرات في قاعدة البيانات
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
تم حفظ التغييرات في قاعدة البيانات
لا توجد بيانات في جدول purchase_invoices، لا حاجة للترحيل.
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
المستخدم الرئيسي موجود بالفعل
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
عرض نافذة تسجيل الدخول...
تم حفظ التغييرات في قاعدة البيانات
نتيجة تسجيل الدخول: 1
بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-28 18:09:48', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-28 18:09:48', 'status': 'نشط'}
جاري إنشاء النافذة الرئيسية...
بدء إنشاء النافذة الرئيسية...
تم استدعاء منشئ الفئة الأب
تم تخزين بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-28 18:09:48', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-28 18:09:48', 'status': 'نشط'}
جاري تعيين الصلاحيات...
جاري إنشاء StackedWidget...
تم إنشاء StackedWidget
جاري إنشاء زر وصفحة: المبيعات
تم إنشاء زر: المبيعات
جاري إنشاء صفحة: المبيعات
خطأ في معالجة المنتجات المفضلة: 'SalesView' object has no attribute 'product_buttons'
تم إنشاء صفحة: المبيعات
تم إضافة صفحة: المبيعات إلى StackedWidget
جاري إنشاء زر وصفحة: المنتجات
تم إنشاء زر: المنتجات
جاري إنشاء صفحة: المنتجات
تم إنشاء صفحة: المنتجات
تم إضافة صفحة: المنتجات إلى StackedWidget
جاري إنشاء زر وصفحة: الفواتير
تم إنشاء زر: الفواتير
جاري إنشاء صفحة: الفواتير
فلتر تاريخ البدء: 2025/05/28
فلتر تاريخ الانتهاء: 2025/05/28 (قبل 2025/05/29)
استعلام البحث: 
            SELECT i.id, i.reference_number, i.date, i.subtotal, i.tax, i.discount, 
                   i.total, i.paid_amount, i.remaining_amount, i.payment_method, i.status, 
                   i.notes, c.name as customer_name, c.id as customer_id
            FROM invoices i
            LEFT JOIN customers c ON i.customer_id = c.id
            WHERE 1=1
         AND (i.date >= ? OR i.date LIKE ?) AND i.date < ?
معلمات البحث: ['2025/05/28', '2025/05/28%', '2025/05/29']
تم العثور على 0 فاتورة
تم إنشاء صفحة: الفواتير
تم إضافة صفحة: الفواتير إلى StackedWidget
جاري إنشاء زر وصفحة: التقارير
تم إنشاء زر: التقارير
جاري إنشاء صفحة: التقارير
Invoices after initial filtering (status, customer): 0
تم إنشاء صفحة: التقارير
تم إضافة صفحة: التقارير إلى StackedWidget
جاري إنشاء زر وصفحة: العملاء
تم إنشاء زر: العملاء
جاري إنشاء صفحة: العملاء
تنسيق التاريخ: 2025/05/25 14:10:01
تم إنشاء صفحة: العملاء
تم إضافة صفحة: العملاء إلى StackedWidget
جاري إنشاء زر وصفحة: الموردين
تم إنشاء زر: الموردين
جاري إنشاء صفحة: الموردين
تم استرجاع 2 مورد من قاعدة البيانات
أول مورد: {'id': 10, 'name': 'احمد ربيع', 'phone': '', 'email': '', 'address': '', 'notes': '', 'contact_person': None, 'total_purchases': 5871.5, 'last_purchase_date': '2025/05/14 10:17:48', 'purchase_count': 1, 'total_debt': 1500.0}
تاريخ آخر عملية شراء للمورد احمد ربيع: 2025/05/14 10:17:48
تنسيق التاريخ: 2025/05/14 10:17:48
تاريخ آخر عملية شراء للمورد عمرو ابو رحمة: 2025/05/14 10:28:43
تنسيق التاريخ: 2025/05/14 10:28:43
تم إنشاء صفحة: الموردين
تم إضافة صفحة: الموردين إلى StackedWidget
جاري إنشاء زر وصفحة: المصروفات
تم إنشاء زر: المصروفات
جاري إنشاء صفحة: المصروفات
تم إنشاء صفحة: المصروفات
تم إضافة صفحة: المصروفات إلى StackedWidget
جاري إنشاء زر وصفحة: الإعدادات
تم إنشاء زر: الإعدادات
جاري إنشاء صفحة: الإعدادات
تم تحميل 2 مستخدم من قاعدة البيانات
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم إنشاء صفحة: الإعدادات
تم إضافة صفحة: الإعدادات إلى StackedWidget
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: جاري تحميل العملاء...
تم العثور على 0 عميل
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: عميل نقدي
تم تحديث صفحة المبيعات
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم قطع الاتصال بقاعدة البيانات
جاري تحديث معلومات المستخدم...
تم إنشاء النافذة الرئيسية بنجاح
تم إنشاء النافذة الرئيسية بنجاح
تم عرض النافذة الرئيسية
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
إجمالي الفاتورة: 85.0 - عدد المنتجات: 1
c:\Users\<USER>\Desktop\11\main.pyw:86: DeprecationWarning: sipPyTypeDict() is deprecated, the extension module should use sipPyTypeDictRef() instead
  class MainWindow(QMainWindow):
استخدام الخط المحمل من الملف: Readex Pro
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم إنشاء الجداول بنجاح
تم حفظ التغييرات في قاعدة البيانات
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
تم حفظ التغييرات في قاعدة البيانات
لا توجد بيانات في جدول purchase_invoices، لا حاجة للترحيل.
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
المستخدم الرئيسي موجود بالفعل
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
عرض نافذة تسجيل الدخول...
تم حفظ التغييرات في قاعدة البيانات
نتيجة تسجيل الدخول: 1
بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-28 18:23:03', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-28 18:23:03', 'status': 'نشط'}
جاري إنشاء النافذة الرئيسية...
بدء إنشاء النافذة الرئيسية...
تم استدعاء منشئ الفئة الأب
تم تخزين بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-28 18:23:03', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-28 18:23:03', 'status': 'نشط'}
جاري تعيين الصلاحيات...
جاري إنشاء StackedWidget...
تم إنشاء StackedWidget
جاري إنشاء زر وصفحة: المبيعات
تم إنشاء زر: المبيعات
جاري إنشاء صفحة: المبيعات
خطأ في معالجة المنتجات المفضلة: 'SalesView' object has no attribute 'product_buttons'
تم إنشاء صفحة: المبيعات
تم إضافة صفحة: المبيعات إلى StackedWidget
جاري إنشاء زر وصفحة: المنتجات
تم إنشاء زر: المنتجات
جاري إنشاء صفحة: المنتجات
تم إنشاء صفحة: المنتجات
تم إضافة صفحة: المنتجات إلى StackedWidget
جاري إنشاء زر وصفحة: الفواتير
تم إنشاء زر: الفواتير
جاري إنشاء صفحة: الفواتير
فلتر تاريخ البدء: 2025/05/28
فلتر تاريخ الانتهاء: 2025/05/28 (قبل 2025/05/29)
استعلام البحث: 
            SELECT i.id, i.reference_number, i.date, i.subtotal, i.tax, i.discount, 
                   i.total, i.paid_amount, i.remaining_amount, i.payment_method, i.status, 
                   i.notes, c.name as customer_name, c.id as customer_id
            FROM invoices i
            LEFT JOIN customers c ON i.customer_id = c.id
            WHERE 1=1
         AND (i.date >= ? OR i.date LIKE ?) AND i.date < ?
معلمات البحث: ['2025/05/28', '2025/05/28%', '2025/05/29']
تم العثور على 0 فاتورة
تم إنشاء صفحة: الفواتير
تم إضافة صفحة: الفواتير إلى StackedWidget
جاري إنشاء زر وصفحة: التقارير
تم إنشاء زر: التقارير
جاري إنشاء صفحة: التقارير
Invoices after initial filtering (status, customer): 0
تم إنشاء صفحة: التقارير
تم إضافة صفحة: التقارير إلى StackedWidget
جاري إنشاء زر وصفحة: العملاء
تم إنشاء زر: العملاء
جاري إنشاء صفحة: العملاء
تنسيق التاريخ: 2025/05/25 14:10:01
تم إنشاء صفحة: العملاء
تم إضافة صفحة: العملاء إلى StackedWidget
جاري إنشاء زر وصفحة: الموردين
تم إنشاء زر: الموردين
جاري إنشاء صفحة: الموردين
تم استرجاع 2 مورد من قاعدة البيانات
أول مورد: {'id': 10, 'name': 'احمد ربيع', 'phone': '', 'email': '', 'address': '', 'notes': '', 'contact_person': None, 'total_purchases': 5871.5, 'last_purchase_date': '2025/05/14 10:17:48', 'purchase_count': 1, 'total_debt': 1500.0}
تاريخ آخر عملية شراء للمورد احمد ربيع: 2025/05/14 10:17:48
تنسيق التاريخ: 2025/05/14 10:17:48
تاريخ آخر عملية شراء للمورد عمرو ابو رحمة: 2025/05/14 10:28:43
تنسيق التاريخ: 2025/05/14 10:28:43
تم إنشاء صفحة: الموردين
تم إضافة صفحة: الموردين إلى StackedWidget
جاري إنشاء زر وصفحة: المصروفات
تم إنشاء زر: المصروفات
جاري إنشاء صفحة: المصروفات
تم إنشاء صفحة: المصروفات
تم إضافة صفحة: المصروفات إلى StackedWidget
جاري إنشاء زر وصفحة: الإعدادات
تم إنشاء زر: الإعدادات
جاري إنشاء صفحة: الإعدادات
تم تحميل 2 مستخدم من قاعدة البيانات
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم إنشاء صفحة: الإعدادات
تم إضافة صفحة: الإعدادات إلى StackedWidget
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: جاري تحميل العملاء...
تم العثور على 0 عميل
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: عميل نقدي
تم تحديث صفحة المبيعات
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم قطع الاتصال بقاعدة البيانات
جاري تحديث معلومات المستخدم...
تم إنشاء النافذة الرئيسية بنجاح
تم إنشاء النافذة الرئيسية بنجاح
تم عرض النافذة الرئيسية
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
إجمالي الفاتورة: 110.0 - عدد المنتجات: 1
c:\Users\<USER>\Desktop\11\main.pyw:86: DeprecationWarning: sipPyTypeDict() is deprecated, the extension module should use sipPyTypeDictRef() instead
  class MainWindow(QMainWindow):
استخدام الخط المحمل من الملف: Readex Pro
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم إنشاء الجداول بنجاح
تم حفظ التغييرات في قاعدة البيانات
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
تم حفظ التغييرات في قاعدة البيانات
لا توجد بيانات في جدول purchase_invoices، لا حاجة للترحيل.
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
المستخدم الرئيسي موجود بالفعل
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
عرض نافذة تسجيل الدخول...
تم حفظ التغييرات في قاعدة البيانات
نتيجة تسجيل الدخول: 1
بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-28 18:24:04', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-28 18:24:04', 'status': 'نشط'}
جاري إنشاء النافذة الرئيسية...
بدء إنشاء النافذة الرئيسية...
تم استدعاء منشئ الفئة الأب
تم تخزين بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-28 18:24:04', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-28 18:24:04', 'status': 'نشط'}
جاري تعيين الصلاحيات...
جاري إنشاء StackedWidget...
تم إنشاء StackedWidget
جاري إنشاء زر وصفحة: المبيعات
تم إنشاء زر: المبيعات
جاري إنشاء صفحة: المبيعات
خطأ في معالجة المنتجات المفضلة: 'SalesView' object has no attribute 'product_buttons'
تم إنشاء صفحة: المبيعات
تم إضافة صفحة: المبيعات إلى StackedWidget
جاري إنشاء زر وصفحة: المنتجات
تم إنشاء زر: المنتجات
جاري إنشاء صفحة: المنتجات
تم إنشاء صفحة: المنتجات
تم إضافة صفحة: المنتجات إلى StackedWidget
جاري إنشاء زر وصفحة: الفواتير
تم إنشاء زر: الفواتير
جاري إنشاء صفحة: الفواتير
فلتر تاريخ البدء: 2025/05/28
فلتر تاريخ الانتهاء: 2025/05/28 (قبل 2025/05/29)
استعلام البحث: 
            SELECT i.id, i.reference_number, i.date, i.subtotal, i.tax, i.discount, 
                   i.total, i.paid_amount, i.remaining_amount, i.payment_method, i.status, 
                   i.notes, c.name as customer_name, c.id as customer_id
            FROM invoices i
            LEFT JOIN customers c ON i.customer_id = c.id
            WHERE 1=1
         AND (i.date >= ? OR i.date LIKE ?) AND i.date < ?
معلمات البحث: ['2025/05/28', '2025/05/28%', '2025/05/29']
تم العثور على 0 فاتورة
تم إنشاء صفحة: الفواتير
تم إضافة صفحة: الفواتير إلى StackedWidget
جاري إنشاء زر وصفحة: التقارير
تم إنشاء زر: التقارير
جاري إنشاء صفحة: التقارير
Invoices after initial filtering (status, customer): 0
تم إنشاء صفحة: التقارير
تم إضافة صفحة: التقارير إلى StackedWidget
جاري إنشاء زر وصفحة: العملاء
تم إنشاء زر: العملاء
جاري إنشاء صفحة: العملاء
تنسيق التاريخ: 2025/05/25 14:10:01
تم إنشاء صفحة: العملاء
تم إضافة صفحة: العملاء إلى StackedWidget
جاري إنشاء زر وصفحة: الموردين
تم إنشاء زر: الموردين
جاري إنشاء صفحة: الموردين
تم استرجاع 2 مورد من قاعدة البيانات
أول مورد: {'id': 10, 'name': 'احمد ربيع', 'phone': '', 'email': '', 'address': '', 'notes': '', 'contact_person': None, 'total_purchases': 5871.5, 'last_purchase_date': '2025/05/14 10:17:48', 'purchase_count': 1, 'total_debt': 1500.0}
تاريخ آخر عملية شراء للمورد احمد ربيع: 2025/05/14 10:17:48
تنسيق التاريخ: 2025/05/14 10:17:48
تاريخ آخر عملية شراء للمورد عمرو ابو رحمة: 2025/05/14 10:28:43
تنسيق التاريخ: 2025/05/14 10:28:43
تم إنشاء صفحة: الموردين
تم إضافة صفحة: الموردين إلى StackedWidget
جاري إنشاء زر وصفحة: المصروفات
تم إنشاء زر: المصروفات
جاري إنشاء صفحة: المصروفات
تم إنشاء صفحة: المصروفات
تم إضافة صفحة: المصروفات إلى StackedWidget
جاري إنشاء زر وصفحة: الإعدادات
تم إنشاء زر: الإعدادات
جاري إنشاء صفحة: الإعدادات
تم تحميل 2 مستخدم من قاعدة البيانات
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم إنشاء صفحة: الإعدادات
تم إضافة صفحة: الإعدادات إلى StackedWidget
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: جاري تحميل العملاء...
تم العثور على 0 عميل
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: عميل نقدي
تم تحديث صفحة المبيعات
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم قطع الاتصال بقاعدة البيانات
جاري تحديث معلومات المستخدم...
تم إنشاء النافذة الرئيسية بنجاح
تم إنشاء النافذة الرئيسية بنجاح
تم عرض النافذة الرئيسية
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
إجمالي الفاتورة: 110.0 - عدد المنتجات: 1
c:\Users\<USER>\Desktop\11\main.pyw:86: DeprecationWarning: sipPyTypeDict() is deprecated, the extension module should use sipPyTypeDictRef() instead
  class MainWindow(QMainWindow):
استخدام الخط المحمل من الملف: Readex Pro
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم إنشاء الجداول بنجاح
تم حفظ التغييرات في قاعدة البيانات
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
تم حفظ التغييرات في قاعدة البيانات
لا توجد بيانات في جدول purchase_invoices، لا حاجة للترحيل.
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
المستخدم الرئيسي موجود بالفعل
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
عرض نافذة تسجيل الدخول...
تم حفظ التغييرات في قاعدة البيانات
نتيجة تسجيل الدخول: 1
بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-28 18:25:19', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-28 18:25:19', 'status': 'نشط'}
جاري إنشاء النافذة الرئيسية...
بدء إنشاء النافذة الرئيسية...
تم استدعاء منشئ الفئة الأب
تم تخزين بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-28 18:25:19', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-28 18:25:19', 'status': 'نشط'}
جاري تعيين الصلاحيات...
جاري إنشاء StackedWidget...
تم إنشاء StackedWidget
جاري إنشاء زر وصفحة: المبيعات
تم إنشاء زر: المبيعات
جاري إنشاء صفحة: المبيعات
خطأ في معالجة المنتجات المفضلة: 'SalesView' object has no attribute 'product_buttons'
تم إنشاء صفحة: المبيعات
تم إضافة صفحة: المبيعات إلى StackedWidget
جاري إنشاء زر وصفحة: المنتجات
تم إنشاء زر: المنتجات
جاري إنشاء صفحة: المنتجات
تم إنشاء صفحة: المنتجات
تم إضافة صفحة: المنتجات إلى StackedWidget
جاري إنشاء زر وصفحة: الفواتير
تم إنشاء زر: الفواتير
جاري إنشاء صفحة: الفواتير
فلتر تاريخ البدء: 2025/05/28
فلتر تاريخ الانتهاء: 2025/05/28 (قبل 2025/05/29)
استعلام البحث: 
            SELECT i.id, i.reference_number, i.date, i.subtotal, i.tax, i.discount, 
                   i.total, i.paid_amount, i.remaining_amount, i.payment_method, i.status, 
                   i.notes, c.name as customer_name, c.id as customer_id
            FROM invoices i
            LEFT JOIN customers c ON i.customer_id = c.id
            WHERE 1=1
         AND (i.date >= ? OR i.date LIKE ?) AND i.date < ?
معلمات البحث: ['2025/05/28', '2025/05/28%', '2025/05/29']
تم العثور على 0 فاتورة
تم إنشاء صفحة: الفواتير
تم إضافة صفحة: الفواتير إلى StackedWidget
جاري إنشاء زر وصفحة: التقارير
تم إنشاء زر: التقارير
جاري إنشاء صفحة: التقارير
Invoices after initial filtering (status, customer): 0
تم إنشاء صفحة: التقارير
تم إضافة صفحة: التقارير إلى StackedWidget
جاري إنشاء زر وصفحة: العملاء
تم إنشاء زر: العملاء
جاري إنشاء صفحة: العملاء
تنسيق التاريخ: 2025/05/25 14:10:01
تم إنشاء صفحة: العملاء
تم إضافة صفحة: العملاء إلى StackedWidget
جاري إنشاء زر وصفحة: الموردين
تم إنشاء زر: الموردين
جاري إنشاء صفحة: الموردين
تم استرجاع 2 مورد من قاعدة البيانات
أول مورد: {'id': 10, 'name': 'احمد ربيع', 'phone': '', 'email': '', 'address': '', 'notes': '', 'contact_person': None, 'total_purchases': 5871.5, 'last_purchase_date': '2025/05/14 10:17:48', 'purchase_count': 1, 'total_debt': 1500.0}
تاريخ آخر عملية شراء للمورد احمد ربيع: 2025/05/14 10:17:48
تنسيق التاريخ: 2025/05/14 10:17:48
تاريخ آخر عملية شراء للمورد عمرو ابو رحمة: 2025/05/14 10:28:43
تنسيق التاريخ: 2025/05/14 10:28:43
تم إنشاء صفحة: الموردين
تم إضافة صفحة: الموردين إلى StackedWidget
جاري إنشاء زر وصفحة: المصروفات
تم إنشاء زر: المصروفات
جاري إنشاء صفحة: المصروفات
تم إنشاء صفحة: المصروفات
تم إضافة صفحة: المصروفات إلى StackedWidget
جاري إنشاء زر وصفحة: الإعدادات
تم إنشاء زر: الإعدادات
جاري إنشاء صفحة: الإعدادات
تم تحميل 2 مستخدم من قاعدة البيانات
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم إنشاء صفحة: الإعدادات
تم إضافة صفحة: الإعدادات إلى StackedWidget
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: جاري تحميل العملاء...
تم العثور على 0 عميل
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: عميل نقدي
تم تحديث صفحة المبيعات
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم قطع الاتصال بقاعدة البيانات
جاري تحديث معلومات المستخدم...
تم إنشاء النافذة الرئيسية بنجاح
تم إنشاء النافذة الرئيسية بنجاح
تم عرض النافذة الرئيسية
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
إجمالي الفاتورة: 160.0 - عدد المنتجات: 1
c:\Users\<USER>\Desktop\11\main.pyw:86: DeprecationWarning: sipPyTypeDict() is deprecated, the extension module should use sipPyTypeDictRef() instead
  class MainWindow(QMainWindow):
استخدام الخط المحمل من الملف: Readex Pro
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم إنشاء الجداول بنجاح
تم حفظ التغييرات في قاعدة البيانات
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
تم حفظ التغييرات في قاعدة البيانات
لا توجد بيانات في جدول purchase_invoices، لا حاجة للترحيل.
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
المستخدم الرئيسي موجود بالفعل
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
عرض نافذة تسجيل الدخول...
تم حفظ التغييرات في قاعدة البيانات
نتيجة تسجيل الدخول: 1
بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-28 18:31:17', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-28 18:31:17', 'status': 'نشط'}
جاري إنشاء النافذة الرئيسية...
بدء إنشاء النافذة الرئيسية...
تم استدعاء منشئ الفئة الأب
تم تخزين بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-28 18:31:17', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-28 18:31:17', 'status': 'نشط'}
جاري تعيين الصلاحيات...
جاري إنشاء StackedWidget...
تم إنشاء StackedWidget
جاري إنشاء زر وصفحة: المبيعات
تم إنشاء زر: المبيعات
جاري إنشاء صفحة: المبيعات
خطأ في معالجة المنتجات المفضلة: 'SalesView' object has no attribute 'product_buttons'
تم إنشاء صفحة: المبيعات
تم إضافة صفحة: المبيعات إلى StackedWidget
جاري إنشاء زر وصفحة: المنتجات
تم إنشاء زر: المنتجات
جاري إنشاء صفحة: المنتجات
تم إنشاء صفحة: المنتجات
تم إضافة صفحة: المنتجات إلى StackedWidget
جاري إنشاء زر وصفحة: الفواتير
تم إنشاء زر: الفواتير
جاري إنشاء صفحة: الفواتير
فلتر تاريخ البدء: 2025/05/28
فلتر تاريخ الانتهاء: 2025/05/28 (قبل 2025/05/29)
استعلام البحث: 
            SELECT i.id, i.reference_number, i.date, i.subtotal, i.tax, i.discount, 
                   i.total, i.paid_amount, i.remaining_amount, i.payment_method, i.status, 
                   i.notes, c.name as customer_name, c.id as customer_id
            FROM invoices i
            LEFT JOIN customers c ON i.customer_id = c.id
            WHERE 1=1
         AND (i.date >= ? OR i.date LIKE ?) AND i.date < ?
معلمات البحث: ['2025/05/28', '2025/05/28%', '2025/05/29']
تم العثور على 0 فاتورة
تم إنشاء صفحة: الفواتير
تم إضافة صفحة: الفواتير إلى StackedWidget
جاري إنشاء زر وصفحة: التقارير
تم إنشاء زر: التقارير
جاري إنشاء صفحة: التقارير
Invoices after initial filtering (status, customer): 0
تم إنشاء صفحة: التقارير
تم إضافة صفحة: التقارير إلى StackedWidget
جاري إنشاء زر وصفحة: العملاء
تم إنشاء زر: العملاء
جاري إنشاء صفحة: العملاء
تنسيق التاريخ: 2025/05/25 14:10:01
تم إنشاء صفحة: العملاء
تم إضافة صفحة: العملاء إلى StackedWidget
جاري إنشاء زر وصفحة: الموردين
تم إنشاء زر: الموردين
جاري إنشاء صفحة: الموردين
تم استرجاع 2 مورد من قاعدة البيانات
أول مورد: {'id': 10, 'name': 'احمد ربيع', 'phone': '', 'email': '', 'address': '', 'notes': '', 'contact_person': None, 'total_purchases': 5871.5, 'last_purchase_date': '2025/05/14 10:17:48', 'purchase_count': 1, 'total_debt': 1500.0}
تاريخ آخر عملية شراء للمورد احمد ربيع: 2025/05/14 10:17:48
تنسيق التاريخ: 2025/05/14 10:17:48
تاريخ آخر عملية شراء للمورد عمرو ابو رحمة: 2025/05/14 10:28:43
تنسيق التاريخ: 2025/05/14 10:28:43
تم إنشاء صفحة: الموردين
تم إضافة صفحة: الموردين إلى StackedWidget
جاري إنشاء زر وصفحة: المصروفات
تم إنشاء زر: المصروفات
جاري إنشاء صفحة: المصروفات
تم إنشاء صفحة: المصروفات
تم إضافة صفحة: المصروفات إلى StackedWidget
جاري إنشاء زر وصفحة: الإعدادات
تم إنشاء زر: الإعدادات
جاري إنشاء صفحة: الإعدادات
تم تحميل 2 مستخدم من قاعدة البيانات
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم إنشاء صفحة: الإعدادات
تم إضافة صفحة: الإعدادات إلى StackedWidget
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: جاري تحميل العملاء...
تم العثور على 0 عميل
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: عميل نقدي
تم تحديث صفحة المبيعات
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم قطع الاتصال بقاعدة البيانات
جاري تحديث معلومات المستخدم...
تم إنشاء النافذة الرئيسية بنجاح
تم إنشاء النافذة الرئيسية بنجاح
تم عرض النافذة الرئيسية
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
إجمالي الفاتورة: 160.0 - عدد المنتجات: 1
تم تحديث صفحة المنتجات
تم قطع الاتصال بقاعدة البيانات
فلتر تاريخ البدء: 2025/05/28
فلتر تاريخ الانتهاء: 2025/05/28 (قبل 2025/05/29)
استعلام البحث: 
            SELECT i.id, i.reference_number, i.date, i.subtotal, i.tax, i.discount, 
                   i.total, i.paid_amount, i.remaining_amount, i.payment_method, i.status, 
                   i.notes, c.name as customer_name, c.id as customer_id
            FROM invoices i
            LEFT JOIN customers c ON i.customer_id = c.id
            WHERE 1=1
         AND (i.date >= ? OR i.date LIKE ?) AND i.date < ?
معلمات البحث: ['2025/05/28', '2025/05/28%', '2025/05/29']
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم العثور على 0 فاتورة
تم تحديث صفحة الفواتير
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم حفظ التغييرات في قاعدة البيانات
تم حفظ التغييرات في قاعدة البيانات
تم حفظ التغييرات في قاعدة البيانات
c:\Users\<USER>\Desktop\11\main.pyw:86: DeprecationWarning: sipPyTypeDict() is deprecated, the extension module should use sipPyTypeDictRef() instead
  class MainWindow(QMainWindow):
استخدام الخط المحمل من الملف: Readex Pro
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم إنشاء الجداول بنجاح
تم حفظ التغييرات في قاعدة البيانات
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
تم حفظ التغييرات في قاعدة البيانات
لا توجد بيانات في جدول purchase_invoices، لا حاجة للترحيل.
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
المستخدم الرئيسي موجود بالفعل
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
عرض نافذة تسجيل الدخول...
تم حفظ التغييرات في قاعدة البيانات
نتيجة تسجيل الدخول: 1
بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-28 19:42:59', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-28 19:42:59', 'status': 'نشط'}
جاري إنشاء النافذة الرئيسية...
بدء إنشاء النافذة الرئيسية...
تم استدعاء منشئ الفئة الأب
تم تخزين بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-28 19:42:59', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-28 19:42:59', 'status': 'نشط'}
جاري تعيين الصلاحيات...
جاري إنشاء StackedWidget...
تم إنشاء StackedWidget
جاري إنشاء زر وصفحة: المبيعات
تم إنشاء زر: المبيعات
جاري إنشاء صفحة: المبيعات
خطأ في معالجة المنتجات المفضلة: 'SalesView' object has no attribute 'product_buttons'
تم إنشاء صفحة: المبيعات
تم إضافة صفحة: المبيعات إلى StackedWidget
جاري إنشاء زر وصفحة: المنتجات
تم إنشاء زر: المنتجات
جاري إنشاء صفحة: المنتجات
تم إنشاء صفحة: المنتجات
تم إضافة صفحة: المنتجات إلى StackedWidget
جاري إنشاء زر وصفحة: الفواتير
تم إنشاء زر: الفواتير
جاري إنشاء صفحة: الفواتير
فلتر تاريخ البدء: 2025/05/31
فلتر تاريخ الانتهاء: 2025/05/31 (قبل 2025/06/01)
استعلام البحث: 
            SELECT i.id, i.reference_number, i.date, i.subtotal, i.tax, i.discount, 
                   i.total, i.paid_amount, i.remaining_amount, i.payment_method, i.status, 
                   i.notes, c.name as customer_name, c.id as customer_id
            FROM invoices i
            LEFT JOIN customers c ON i.customer_id = c.id
            WHERE 1=1
         AND (i.date >= ? OR i.date LIKE ?) AND i.date < ?
معلمات البحث: ['2025/05/31', '2025/05/31%', '2025/06/01']
تم العثور على 0 فاتورة
تم إنشاء صفحة: الفواتير
تم إضافة صفحة: الفواتير إلى StackedWidget
جاري إنشاء زر وصفحة: التقارير
تم إنشاء زر: التقارير
جاري إنشاء صفحة: التقارير
Invoices after initial filtering (status, customer): 0
تم إنشاء صفحة: التقارير
تم إضافة صفحة: التقارير إلى StackedWidget
جاري إنشاء زر وصفحة: العملاء
تم إنشاء زر: العملاء
جاري إنشاء صفحة: العملاء
تنسيق التاريخ: 2025/05/25 14:10:01
تم إنشاء صفحة: العملاء
تم إضافة صفحة: العملاء إلى StackedWidget
جاري إنشاء زر وصفحة: الموردين
تم إنشاء زر: الموردين
جاري إنشاء صفحة: الموردين
تم استرجاع 2 مورد من قاعدة البيانات
أول مورد: {'id': 10, 'name': 'احمد ربيع', 'phone': '', 'email': '', 'address': '', 'notes': '', 'contact_person': None, 'total_purchases': 5871.5, 'last_purchase_date': '2025/05/14 10:17:48', 'purchase_count': 1, 'total_debt': 1500.0}
تاريخ آخر عملية شراء للمورد احمد ربيع: 2025/05/14 10:17:48
تنسيق التاريخ: 2025/05/14 10:17:48
تاريخ آخر عملية شراء للمورد عمرو ابو رحمة: 2025/05/14 10:28:43
تنسيق التاريخ: 2025/05/14 10:28:43
تم إنشاء صفحة: الموردين
تم إضافة صفحة: الموردين إلى StackedWidget
جاري إنشاء زر وصفحة: المصروفات
تم إنشاء زر: المصروفات
جاري إنشاء صفحة: المصروفات
تم إنشاء صفحة: المصروفات
تم إضافة صفحة: المصروفات إلى StackedWidget
جاري إنشاء زر وصفحة: الإعدادات
تم إنشاء زر: الإعدادات
جاري إنشاء صفحة: الإعدادات
تم تحميل 2 مستخدم من قاعدة البيانات
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم إنشاء صفحة: الإعدادات
تم إضافة صفحة: الإعدادات إلى StackedWidget
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: جاري تحميل العملاء...
تم العثور على 0 عميل
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: عميل نقدي
تم تحديث صفحة المبيعات
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم قطع الاتصال بقاعدة البيانات
جاري تحديث معلومات المستخدم...
تم إنشاء النافذة الرئيسية بنجاح
تم إنشاء النافذة الرئيسية بنجاح
تم عرض النافذة الرئيسية
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم تحديث صفحة المنتجات
تم قطع الاتصال بقاعدة البيانات
فلتر تاريخ البدء: 2025/05/31
فلتر تاريخ الانتهاء: 2025/05/31 (قبل 2025/06/01)
استعلام البحث: 
            SELECT i.id, i.reference_number, i.date, i.subtotal, i.tax, i.discount, 
                   i.total, i.paid_amount, i.remaining_amount, i.payment_method, i.status, 
                   i.notes, c.name as customer_name, c.id as customer_id
            FROM invoices i
            LEFT JOIN customers c ON i.customer_id = c.id
            WHERE 1=1
         AND (i.date >= ? OR i.date LIKE ?) AND i.date < ?
معلمات البحث: ['2025/05/31', '2025/05/31%', '2025/06/01']
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم العثور على 0 فاتورة
تم تحديث صفحة الفواتير
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
Invoices after initial filtering (status, customer): 0
تم تحديث صفحة التقارير
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تنسيق التاريخ: 2025/05/25 14:10:01
تم تحديث صفحة العملاء
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم استرجاع 2 مورد من قاعدة البيانات
أول مورد: {'id': 10, 'name': 'احمد ربيع', 'phone': '', 'email': '', 'address': '', 'notes': '', 'contact_person': None, 'total_purchases': 5871.5, 'last_purchase_date': '2025/05/14 10:17:48', 'purchase_count': 1, 'total_debt': 1500.0}
تاريخ آخر عملية شراء للمورد احمد ربيع: 2025/05/14 10:17:48
تنسيق التاريخ: 2025/05/14 10:17:48
تاريخ آخر عملية شراء للمورد عمرو ابو رحمة: 2025/05/14 10:28:43
تنسيق التاريخ: 2025/05/14 10:28:43
تم تحديث صفحة الموردين
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
[DEBUG] Obteniendo compras para el proveedor ID: 10
[DEBUG] ID de proveedor validado como entero: 10
[DEBUG] Compras encontradas con SupplierModel.get_supplier_purchases: 1
[DEBUG] Primera compra encontrada: {'id': 8, 'reference_number': 'PO-2025-05-14-101748', 'purchase_date': '2025/05/14 10:17:48', 'total_amount': 5871.5, 'paid_amount': 4371.5, 'remaining_amount': 1500.0, 'payment_status': 'غير مدفوعة', 'notes': '', 'supplier_name': 'احمد ربيع', 'item_count': 14}
[DEBUG] تحديث المشتريات للمورد ذو المعرف: 10
[DEBUG] تم العثور على 2 فاتورة في النظام
[DEBUG] معرفات الموردين المتوفرة في الفواتير: {'11', '10'}
[DEBUG] معرف المورد الذي نبحث عنه: [10, '10', 10]
[DEBUG] تمت إضافة الفاتورة رقم 8 للقائمة (supplier_id=10)
[DEBUG] تم العثور على 1 فاتورة لهذا المورد
[DEBUG] تم تحديث سجل المشتريات. تم العثور على 1 عملية شراء.
[DEBUG] عرض تفاصيل الفاتورة رقم 8
[DEBUG] عدد العناصر المستلمة: 14
[DEBUG] العنصر 1: None - الكمية: 5 - الإجمالي: 470.0
[DEBUG] العنصر 2: None - الكمية: 10 - الإجمالي: 710.0
[DEBUG] العنصر 3: None - الكمية: 10 - الإجمالي: 270.0
[DEBUG] العنصر 4: None - الكمية: 3 - الإجمالي: 720.0
[DEBUG] العنصر 5: None - الكمية: 3 - الإجمالي: 690.0
[DEBUG] العنصر 6: None - الكمية: 10 - الإجمالي: 230.0
[DEBUG] العنصر 7: None - الكمية: 1 - الإجمالي: 105.0
[DEBUG] العنصر 8: None - الكمية: 1 - الإجمالي: 115.0
[DEBUG] العنصر 9: None - الكمية: 4 - الإجمالي: 248.0
[DEBUG] العنصر 10: None - الكمية: 4 - الإجمالي: 288.0
[DEBUG] العنصر 11: None - الكمية: 2 - الإجمالي: 54.0
[DEBUG] العنصر 12: None - الكمية: 3 - الإجمالي: 129.0
[DEBUG] العنصر 13: None - الكمية: 115 - الإجمالي: 1552.5
[DEBUG] العنصر 14: None - الكمية: 10 - الإجمالي: 290.0
[DEBUG] ملء جدول العناصر بـ 14 عنصر
[DEBUG] تمت إضافة العنصر 1: شاحن ستارت سوبر فوك ميكرو, الكمية: 5, الإجمالي: 470.00
[DEBUG] تمت إضافة العنصر 2: شاحن ستارت دورار ميكرو, الكمية: 10, الإجمالي: 710.00
[DEBUG] تمت إضافة العنصر 3: كبل ستارت 120w ميكرو, الكمية: 10, الإجمالي: 270.00
[DEBUG] تمت إضافة العنصر 4: ايربودز برو 2, الكمية: 3, الإجمالي: 720.00
[DEBUG] تمت إضافة العنصر 5: ايربودز برو 3, الكمية: 3, الإجمالي: 690.00
[DEBUG] تمت إضافة العنصر 6: بطارية نوكيا, الكمية: 10, الإجمالي: 230.00
[DEBUG] تمت إضافة العنصر 7: فلاشة 16G, الكمية: 1, الإجمالي: 105.00
[DEBUG] تمت إضافة العنصر 8: فلاشة 32G, الكمية: 1, الإجمالي: 115.00
[DEBUG] تمت إضافة العنصر 9: ميموري 4G, الكمية: 4, الإجمالي: 248.00
[DEBUG] تمت إضافة العنصر 10: ميموري 8G, الكمية: 4, الإجمالي: 288.00
[DEBUG] تمت إضافة العنصر 11: استيك سيلكون, الكمية: 2, الإجمالي: 54.00
[DEBUG] تمت إضافة العنصر 12: استيك قماش, الكمية: 3, الإجمالي: 129.00
[DEBUG] تمت إضافة العنصر 13: اسكرينة HD ULTRA, الكمية: 115, الإجمالي: 1552.50
[DEBUG] تمت إضافة العنصر 14: كبل ستارت 120W تايب سي, الكمية: 10, الإجمالي: 290.00
[DEBUG] Iniciando proceso de añadir compra para proveedor ID: 10
[DEBUG] Iniciando proceso de añadir compra para proveedor ID: 10
[DEBUG] La compra no se añadió correctamente, manteniendo diálogo actual
[DEBUG] Resultado de add_purchase_for_supplier: False
[DEBUG] La compra no se añadió correctamente, manteniendo diálogo actual
تم قطع الاتصال بقاعدة البيانات
تم تحديث صفحة الإعدادات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم قطع الاتصال بقاعدة البيانات
تم تحديث محتوى تاب المستخدمين
تم تحديث محتوى تاب قاعدة البيانات والنسخ الاحتياطي
تم تحديث محتوى تاب المستخدمين
تم تحديث محتوى تاب عام
C:\Users\<USER>\Desktop\11\main.pyw:86: DeprecationWarning: sipPyTypeDict() is deprecated, the extension module should use sipPyTypeDictRef() instead
  class MainWindow(QMainWindow):
استخدام الخط المحمل من الملف: Readex Pro
تم تغيير السمة إلى: فاتح
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم إنشاء الجداول بنجاح
تم حفظ التغييرات في قاعدة البيانات
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
تم حفظ التغييرات في قاعدة البيانات
لا توجد بيانات في جدول purchase_invoices، لا حاجة للترحيل.
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
المستخدم الرئيسي موجود بالفعل
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
عرض نافذة تسجيل الدخول...
تم حفظ التغييرات في قاعدة البيانات
نتيجة تسجيل الدخول: 1
بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-31 16:39:11', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-31 16:39:11', 'status': 'نشط'}
جاري إنشاء النافذة الرئيسية...
بدء إنشاء النافذة الرئيسية...
تم استدعاء منشئ الفئة الأب
تم تخزين بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-31 16:39:11', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-31 16:39:11', 'status': 'نشط'}
جاري تعيين الصلاحيات...
جاري إنشاء StackedWidget...
تم إنشاء StackedWidget
جاري إنشاء زر وصفحة: المبيعات
تم إنشاء زر: المبيعات
جاري إنشاء صفحة: المبيعات
خطأ في معالجة المنتجات المفضلة: 'SalesView' object has no attribute 'product_buttons'
تم إنشاء صفحة: المبيعات
تم إضافة صفحة: المبيعات إلى StackedWidget
جاري إنشاء زر وصفحة: المنتجات
تم إنشاء زر: المنتجات
جاري إنشاء صفحة: المنتجات
تم إنشاء صفحة: المنتجات
تم إضافة صفحة: المنتجات إلى StackedWidget
جاري إنشاء زر وصفحة: الفواتير
تم إنشاء زر: الفواتير
جاري إنشاء صفحة: الفواتير
فلتر تاريخ البدء: 2025/05/31
فلتر تاريخ الانتهاء: 2025/05/31 (قبل 2025/06/01)
استعلام البحث: 
            SELECT i.id, i.reference_number, i.date, i.subtotal, i.tax, i.discount, 
                   i.total, i.paid_amount, i.remaining_amount, i.payment_method, i.status, 
                   i.notes, c.name as customer_name, c.id as customer_id
            FROM invoices i
            LEFT JOIN customers c ON i.customer_id = c.id
            WHERE 1=1
         AND (i.date >= ? OR i.date LIKE ?) AND i.date < ?
معلمات البحث: ['2025/05/31', '2025/05/31%', '2025/06/01']
تم العثور على 0 فاتورة
تم إنشاء صفحة: الفواتير
تم إضافة صفحة: الفواتير إلى StackedWidget
جاري إنشاء زر وصفحة: التقارير
تم إنشاء زر: التقارير
جاري إنشاء صفحة: التقارير
Invoices after initial filtering (status, customer): 0
تم إنشاء صفحة: التقارير
تم إضافة صفحة: التقارير إلى StackedWidget
جاري إنشاء زر وصفحة: العملاء
تم إنشاء زر: العملاء
جاري إنشاء صفحة: العملاء
تنسيق التاريخ: 2025/05/25 14:10:01
تم إنشاء صفحة: العملاء
تم إضافة صفحة: العملاء إلى StackedWidget
جاري إنشاء زر وصفحة: الموردين
تم إنشاء زر: الموردين
جاري إنشاء صفحة: الموردين
تم استرجاع 2 مورد من قاعدة البيانات
أول مورد: {'id': 10, 'name': 'احمد ربيع', 'phone': '', 'email': '', 'address': '', 'notes': '', 'contact_person': None, 'total_purchases': 5871.5, 'last_purchase_date': '2025/05/14 10:17:48', 'purchase_count': 1, 'total_debt': 1500.0}
تاريخ آخر عملية شراء للمورد احمد ربيع: 2025/05/14 10:17:48
تنسيق التاريخ: 2025/05/14 10:17:48
تاريخ آخر عملية شراء للمورد عمرو ابو رحمة: 2025/05/14 10:28:43
تنسيق التاريخ: 2025/05/14 10:28:43
تم إنشاء صفحة: الموردين
تم إضافة صفحة: الموردين إلى StackedWidget
جاري إنشاء زر وصفحة: المصروفات
تم إنشاء زر: المصروفات
جاري إنشاء صفحة: المصروفات
تم إنشاء صفحة: المصروفات
تم إضافة صفحة: المصروفات إلى StackedWidget
جاري إنشاء زر وصفحة: الإعدادات
تم إنشاء زر: الإعدادات
جاري إنشاء صفحة: الإعدادات
تم تحميل 2 مستخدم من قاعدة البيانات
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم إنشاء صفحة: الإعدادات
تم إضافة صفحة: الإعدادات إلى StackedWidget
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: جاري تحميل العملاء...
تم العثور على 0 عميل
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: عميل نقدي
تم تحديث صفحة المبيعات
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم قطع الاتصال بقاعدة البيانات
جاري تحديث معلومات المستخدم...
تم إنشاء النافذة الرئيسية بنجاح
تم إنشاء النافذة الرئيسية بنجاح
تم عرض النافذة الرئيسية
تم تحديث صفحة الإعدادات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم قطع الاتصال بقاعدة البيانات
تم تغيير السمة إلى: داكن
c:\Users\<USER>\Desktop\11\main.pyw:86: DeprecationWarning: sipPyTypeDict() is deprecated, the extension module should use sipPyTypeDictRef() instead
  class MainWindow(QMainWindow):
استخدام الخط المحمل من الملف: Readex Pro
تم تغيير السمة إلى: فاتح
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم إنشاء الجداول بنجاح
تم حفظ التغييرات في قاعدة البيانات
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
تم حفظ التغييرات في قاعدة البيانات
لا توجد بيانات في جدول purchase_invoices، لا حاجة للترحيل.
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
المستخدم الرئيسي موجود بالفعل
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
عرض نافذة تسجيل الدخول...
تم حفظ التغييرات في قاعدة البيانات
نتيجة تسجيل الدخول: 1
بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-31 16:43:42', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-31 16:43:42', 'status': 'نشط'}
جاري إنشاء النافذة الرئيسية...
بدء إنشاء النافذة الرئيسية...
تم استدعاء منشئ الفئة الأب
تم تخزين بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-31 16:43:42', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-31 16:43:42', 'status': 'نشط'}
جاري تعيين الصلاحيات...
جاري إنشاء StackedWidget...
تم إنشاء StackedWidget
جاري إنشاء زر وصفحة: المبيعات
تم إنشاء زر: المبيعات
جاري إنشاء صفحة: المبيعات
خطأ في معالجة المنتجات المفضلة: 'SalesView' object has no attribute 'product_buttons'
تم إنشاء صفحة: المبيعات
تم إضافة صفحة: المبيعات إلى StackedWidget
جاري إنشاء زر وصفحة: المنتجات
تم إنشاء زر: المنتجات
جاري إنشاء صفحة: المنتجات
تم إنشاء صفحة: المنتجات
تم إضافة صفحة: المنتجات إلى StackedWidget
جاري إنشاء زر وصفحة: الفواتير
تم إنشاء زر: الفواتير
جاري إنشاء صفحة: الفواتير
فلتر تاريخ البدء: 2025/05/31
فلتر تاريخ الانتهاء: 2025/05/31 (قبل 2025/06/01)
استعلام البحث: 
            SELECT i.id, i.reference_number, i.date, i.subtotal, i.tax, i.discount, 
                   i.total, i.paid_amount, i.remaining_amount, i.payment_method, i.status, 
                   i.notes, c.name as customer_name, c.id as customer_id
            FROM invoices i
            LEFT JOIN customers c ON i.customer_id = c.id
            WHERE 1=1
         AND (i.date >= ? OR i.date LIKE ?) AND i.date < ?
معلمات البحث: ['2025/05/31', '2025/05/31%', '2025/06/01']
تم العثور على 0 فاتورة
تم إنشاء صفحة: الفواتير
تم إضافة صفحة: الفواتير إلى StackedWidget
جاري إنشاء زر وصفحة: التقارير
تم إنشاء زر: التقارير
جاري إنشاء صفحة: التقارير
Invoices after initial filtering (status, customer): 0
تم إنشاء صفحة: التقارير
تم إضافة صفحة: التقارير إلى StackedWidget
جاري إنشاء زر وصفحة: العملاء
تم إنشاء زر: العملاء
جاري إنشاء صفحة: العملاء
تنسيق التاريخ: 2025/05/25 14:10:01
تم إنشاء صفحة: العملاء
تم إضافة صفحة: العملاء إلى StackedWidget
جاري إنشاء زر وصفحة: الموردين
تم إنشاء زر: الموردين
جاري إنشاء صفحة: الموردين
تم استرجاع 2 مورد من قاعدة البيانات
أول مورد: {'id': 10, 'name': 'احمد ربيع', 'phone': '', 'email': '', 'address': '', 'notes': '', 'contact_person': None, 'total_purchases': 5871.5, 'last_purchase_date': '2025/05/14 10:17:48', 'purchase_count': 1, 'total_debt': 1500.0}
تاريخ آخر عملية شراء للمورد احمد ربيع: 2025/05/14 10:17:48
تنسيق التاريخ: 2025/05/14 10:17:48
تاريخ آخر عملية شراء للمورد عمرو ابو رحمة: 2025/05/14 10:28:43
تنسيق التاريخ: 2025/05/14 10:28:43
تم إنشاء صفحة: الموردين
تم إضافة صفحة: الموردين إلى StackedWidget
جاري إنشاء زر وصفحة: المصروفات
تم إنشاء زر: المصروفات
جاري إنشاء صفحة: المصروفات
تم إنشاء صفحة: المصروفات
تم إضافة صفحة: المصروفات إلى StackedWidget
جاري إنشاء زر وصفحة: الإعدادات
تم إنشاء زر: الإعدادات
جاري إنشاء صفحة: الإعدادات
تم تحميل 2 مستخدم من قاعدة البيانات
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم إنشاء صفحة: الإعدادات
تم إضافة صفحة: الإعدادات إلى StackedWidget
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: جاري تحميل العملاء...
تم العثور على 0 عميل
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: عميل نقدي
تم تحديث صفحة المبيعات
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم قطع الاتصال بقاعدة البيانات
جاري تحديث معلومات المستخدم...
تم إنشاء النافذة الرئيسية بنجاح
تم إنشاء النافذة الرئيسية بنجاح
تم عرض النافذة الرئيسية
تم تحديث صفحة الإعدادات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم قطع الاتصال بقاعدة البيانات
تم اختيار العميل: 
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم العثور على 1 عميل
تم اختيار العميل: جاري تحميل العملاء...
تم العثور على 0 عميل
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: عميل نقدي
تم تحديث صفحة المبيعات
تم قطع الاتصال بقاعدة البيانات
C:\Users\<USER>\Desktop\11\main.pyw:86: DeprecationWarning: sipPyTypeDict() is deprecated, the extension module should use sipPyTypeDictRef() instead
  class MainWindow(QMainWindow):
استخدام الخط المحمل من الملف: Readex Pro
تم تغيير السمة إلى: فاتح
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم إنشاء الجداول بنجاح
تم حفظ التغييرات في قاعدة البيانات
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
تم حفظ التغييرات في قاعدة البيانات
لا توجد بيانات في جدول purchase_invoices، لا حاجة للترحيل.
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
المستخدم الرئيسي موجود بالفعل
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
عرض نافذة تسجيل الدخول...
تم حفظ التغييرات في قاعدة البيانات
نتيجة تسجيل الدخول: 1
بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-31 16:43:56', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-31 16:43:56', 'status': 'نشط'}
جاري إنشاء النافذة الرئيسية...
بدء إنشاء النافذة الرئيسية...
تم استدعاء منشئ الفئة الأب
تم تخزين بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-31 16:43:56', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-31 16:43:56', 'status': 'نشط'}
جاري تعيين الصلاحيات...
جاري إنشاء StackedWidget...
تم إنشاء StackedWidget
جاري إنشاء زر وصفحة: المبيعات
تم إنشاء زر: المبيعات
جاري إنشاء صفحة: المبيعات
خطأ في معالجة المنتجات المفضلة: 'SalesView' object has no attribute 'product_buttons'
تم إنشاء صفحة: المبيعات
تم إضافة صفحة: المبيعات إلى StackedWidget
جاري إنشاء زر وصفحة: المنتجات
تم إنشاء زر: المنتجات
جاري إنشاء صفحة: المنتجات
تم إنشاء صفحة: المنتجات
تم إضافة صفحة: المنتجات إلى StackedWidget
جاري إنشاء زر وصفحة: الفواتير
تم إنشاء زر: الفواتير
جاري إنشاء صفحة: الفواتير
فلتر تاريخ البدء: 2025/05/31
فلتر تاريخ الانتهاء: 2025/05/31 (قبل 2025/06/01)
استعلام البحث: 
            SELECT i.id, i.reference_number, i.date, i.subtotal, i.tax, i.discount, 
                   i.total, i.paid_amount, i.remaining_amount, i.payment_method, i.status, 
                   i.notes, c.name as customer_name, c.id as customer_id
            FROM invoices i
            LEFT JOIN customers c ON i.customer_id = c.id
            WHERE 1=1
         AND (i.date >= ? OR i.date LIKE ?) AND i.date < ?
معلمات البحث: ['2025/05/31', '2025/05/31%', '2025/06/01']
تم العثور على 0 فاتورة
تم إنشاء صفحة: الفواتير
تم إضافة صفحة: الفواتير إلى StackedWidget
جاري إنشاء زر وصفحة: التقارير
تم إنشاء زر: التقارير
جاري إنشاء صفحة: التقارير
Invoices after initial filtering (status, customer): 0
تم إنشاء صفحة: التقارير
تم إضافة صفحة: التقارير إلى StackedWidget
جاري إنشاء زر وصفحة: العملاء
تم إنشاء زر: العملاء
جاري إنشاء صفحة: العملاء
تنسيق التاريخ: 2025/05/25 14:10:01
تم إنشاء صفحة: العملاء
تم إضافة صفحة: العملاء إلى StackedWidget
جاري إنشاء زر وصفحة: الموردين
تم إنشاء زر: الموردين
جاري إنشاء صفحة: الموردين
تم استرجاع 2 مورد من قاعدة البيانات
أول مورد: {'id': 10, 'name': 'احمد ربيع', 'phone': '', 'email': '', 'address': '', 'notes': '', 'contact_person': None, 'total_purchases': 5871.5, 'last_purchase_date': '2025/05/14 10:17:48', 'purchase_count': 1, 'total_debt': 1500.0}
تاريخ آخر عملية شراء للمورد احمد ربيع: 2025/05/14 10:17:48
تنسيق التاريخ: 2025/05/14 10:17:48
تاريخ آخر عملية شراء للمورد عمرو ابو رحمة: 2025/05/14 10:28:43
تنسيق التاريخ: 2025/05/14 10:28:43
تم إنشاء صفحة: الموردين
تم إضافة صفحة: الموردين إلى StackedWidget
جاري إنشاء زر وصفحة: المصروفات
تم إنشاء زر: المصروفات
جاري إنشاء صفحة: المصروفات
تم إنشاء صفحة: المصروفات
تم إضافة صفحة: المصروفات إلى StackedWidget
جاري إنشاء زر وصفحة: الإعدادات
تم إنشاء زر: الإعدادات
جاري إنشاء صفحة: الإعدادات
تم تحميل 2 مستخدم من قاعدة البيانات
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم تغيير السمة إلى: داكن
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم إنشاء صفحة: الإعدادات
تم إضافة صفحة: الإعدادات إلى StackedWidget
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: جاري تحميل العملاء...
تم العثور على 0 عميل
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: عميل نقدي
تم تحديث صفحة المبيعات
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم قطع الاتصال بقاعدة البيانات
جاري تحديث معلومات المستخدم...
تم إنشاء النافذة الرئيسية بنجاح
تم إنشاء النافذة الرئيسية بنجاح
تم عرض النافذة الرئيسية
تم تحديث صفحة الإعدادات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم قطع الاتصال بقاعدة البيانات
تم تغيير السمة إلى: فاتح
c:\Users\<USER>\Desktop\11\main.pyw:86: DeprecationWarning: sipPyTypeDict() is deprecated, the extension module should use sipPyTypeDictRef() instead
  class MainWindow(QMainWindow):
استخدام الخط المحمل من الملف: Readex Pro
تم تغيير السمة إلى: فاتح
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم إنشاء الجداول بنجاح
تم حفظ التغييرات في قاعدة البيانات
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
تم حفظ التغييرات في قاعدة البيانات
لا توجد بيانات في جدول purchase_invoices، لا حاجة للترحيل.
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
المستخدم الرئيسي موجود بالفعل
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
عرض نافذة تسجيل الدخول...
تم حفظ التغييرات في قاعدة البيانات
نتيجة تسجيل الدخول: 1
بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-31 16:46:22', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-31 16:46:22', 'status': 'نشط'}
جاري إنشاء النافذة الرئيسية...
بدء إنشاء النافذة الرئيسية...
تم استدعاء منشئ الفئة الأب
تم تخزين بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-31 16:46:22', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-31 16:46:22', 'status': 'نشط'}
جاري تعيين الصلاحيات...
جاري إنشاء StackedWidget...
تم إنشاء StackedWidget
جاري إنشاء زر وصفحة: المبيعات
تم إنشاء زر: المبيعات
جاري إنشاء صفحة: المبيعات
خطأ في معالجة المنتجات المفضلة: 'SalesView' object has no attribute 'product_buttons'
تم إنشاء صفحة: المبيعات
تم إضافة صفحة: المبيعات إلى StackedWidget
جاري إنشاء زر وصفحة: المنتجات
تم إنشاء زر: المنتجات
جاري إنشاء صفحة: المنتجات
تم إنشاء صفحة: المنتجات
تم إضافة صفحة: المنتجات إلى StackedWidget
جاري إنشاء زر وصفحة: الفواتير
تم إنشاء زر: الفواتير
جاري إنشاء صفحة: الفواتير
فلتر تاريخ البدء: 2025/05/31
فلتر تاريخ الانتهاء: 2025/05/31 (قبل 2025/06/01)
استعلام البحث: 
            SELECT i.id, i.reference_number, i.date, i.subtotal, i.tax, i.discount, 
                   i.total, i.paid_amount, i.remaining_amount, i.payment_method, i.status, 
                   i.notes, c.name as customer_name, c.id as customer_id
            FROM invoices i
            LEFT JOIN customers c ON i.customer_id = c.id
            WHERE 1=1
         AND (i.date >= ? OR i.date LIKE ?) AND i.date < ?
معلمات البحث: ['2025/05/31', '2025/05/31%', '2025/06/01']
تم العثور على 0 فاتورة
تم إنشاء صفحة: الفواتير
تم إضافة صفحة: الفواتير إلى StackedWidget
جاري إنشاء زر وصفحة: التقارير
تم إنشاء زر: التقارير
جاري إنشاء صفحة: التقارير
Invoices after initial filtering (status, customer): 0
تم إنشاء صفحة: التقارير
تم إضافة صفحة: التقارير إلى StackedWidget
جاري إنشاء زر وصفحة: العملاء
تم إنشاء زر: العملاء
جاري إنشاء صفحة: العملاء
تنسيق التاريخ: 2025/05/25 14:10:01
تم إنشاء صفحة: العملاء
تم إضافة صفحة: العملاء إلى StackedWidget
جاري إنشاء زر وصفحة: الموردين
تم إنشاء زر: الموردين
جاري إنشاء صفحة: الموردين
تم استرجاع 2 مورد من قاعدة البيانات
أول مورد: {'id': 10, 'name': 'احمد ربيع', 'phone': '', 'email': '', 'address': '', 'notes': '', 'contact_person': None, 'total_purchases': 5871.5, 'last_purchase_date': '2025/05/14 10:17:48', 'purchase_count': 1, 'total_debt': 1500.0}
تاريخ آخر عملية شراء للمورد احمد ربيع: 2025/05/14 10:17:48
تنسيق التاريخ: 2025/05/14 10:17:48
تاريخ آخر عملية شراء للمورد عمرو ابو رحمة: 2025/05/14 10:28:43
تنسيق التاريخ: 2025/05/14 10:28:43
تم إنشاء صفحة: الموردين
تم إضافة صفحة: الموردين إلى StackedWidget
جاري إنشاء زر وصفحة: المصروفات
تم إنشاء زر: المصروفات
جاري إنشاء صفحة: المصروفات
تم إنشاء صفحة: المصروفات
تم إضافة صفحة: المصروفات إلى StackedWidget
جاري إنشاء زر وصفحة: الإعدادات
تم إنشاء زر: الإعدادات
جاري إنشاء صفحة: الإعدادات
تم تحميل 2 مستخدم من قاعدة البيانات
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم إنشاء صفحة: الإعدادات
تم إضافة صفحة: الإعدادات إلى StackedWidget
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: جاري تحميل العملاء...
تم العثور على 0 عميل
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: عميل نقدي
تم تحديث صفحة المبيعات
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم قطع الاتصال بقاعدة البيانات
جاري تحديث معلومات المستخدم...
تم إنشاء النافذة الرئيسية بنجاح
تم إنشاء النافذة الرئيسية بنجاح
تم عرض النافذة الرئيسية
تم تحديث صفحة الإعدادات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم قطع الاتصال بقاعدة البيانات
تم اختيار العميل: 
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم العثور على 1 عميل
تم اختيار العميل: جاري تحميل العملاء...
تم العثور على 0 عميل
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: عميل نقدي
تم تحديث صفحة المبيعات
تم قطع الاتصال بقاعدة البيانات
C:\Users\<USER>\Desktop\11\main.pyw:86: DeprecationWarning: sipPyTypeDict() is deprecated, the extension module should use sipPyTypeDictRef() instead
  class MainWindow(QMainWindow):
استخدام الخط المحمل من الملف: Readex Pro
تم تغيير السمة إلى: فاتح
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم إنشاء الجداول بنجاح
تم حفظ التغييرات في قاعدة البيانات
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
تم حفظ التغييرات في قاعدة البيانات
لا توجد بيانات في جدول purchase_invoices، لا حاجة للترحيل.
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
المستخدم الرئيسي موجود بالفعل
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
عرض نافذة تسجيل الدخول...
تم حفظ التغييرات في قاعدة البيانات
نتيجة تسجيل الدخول: 1
بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-31 16:46:36', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-31 16:46:36', 'status': 'نشط'}
جاري إنشاء النافذة الرئيسية...
بدء إنشاء النافذة الرئيسية...
تم استدعاء منشئ الفئة الأب
تم تخزين بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-31 16:46:36', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-31 16:46:36', 'status': 'نشط'}
جاري تعيين الصلاحيات...
جاري إنشاء StackedWidget...
تم إنشاء StackedWidget
جاري إنشاء زر وصفحة: المبيعات
تم إنشاء زر: المبيعات
جاري إنشاء صفحة: المبيعات
خطأ في معالجة المنتجات المفضلة: 'SalesView' object has no attribute 'product_buttons'
تم إنشاء صفحة: المبيعات
تم إضافة صفحة: المبيعات إلى StackedWidget
جاري إنشاء زر وصفحة: المنتجات
تم إنشاء زر: المنتجات
جاري إنشاء صفحة: المنتجات
تم إنشاء صفحة: المنتجات
تم إضافة صفحة: المنتجات إلى StackedWidget
جاري إنشاء زر وصفحة: الفواتير
تم إنشاء زر: الفواتير
جاري إنشاء صفحة: الفواتير
فلتر تاريخ البدء: 2025/05/31
فلتر تاريخ الانتهاء: 2025/05/31 (قبل 2025/06/01)
استعلام البحث: 
            SELECT i.id, i.reference_number, i.date, i.subtotal, i.tax, i.discount, 
                   i.total, i.paid_amount, i.remaining_amount, i.payment_method, i.status, 
                   i.notes, c.name as customer_name, c.id as customer_id
            FROM invoices i
            LEFT JOIN customers c ON i.customer_id = c.id
            WHERE 1=1
         AND (i.date >= ? OR i.date LIKE ?) AND i.date < ?
معلمات البحث: ['2025/05/31', '2025/05/31%', '2025/06/01']
تم العثور على 0 فاتورة
تم إنشاء صفحة: الفواتير
تم إضافة صفحة: الفواتير إلى StackedWidget
جاري إنشاء زر وصفحة: التقارير
تم إنشاء زر: التقارير
جاري إنشاء صفحة: التقارير
Invoices after initial filtering (status, customer): 0
تم إنشاء صفحة: التقارير
تم إضافة صفحة: التقارير إلى StackedWidget
جاري إنشاء زر وصفحة: العملاء
تم إنشاء زر: العملاء
جاري إنشاء صفحة: العملاء
تنسيق التاريخ: 2025/05/25 14:10:01
تم إنشاء صفحة: العملاء
تم إضافة صفحة: العملاء إلى StackedWidget
جاري إنشاء زر وصفحة: الموردين
تم إنشاء زر: الموردين
جاري إنشاء صفحة: الموردين
تم استرجاع 2 مورد من قاعدة البيانات
أول مورد: {'id': 10, 'name': 'احمد ربيع', 'phone': '', 'email': '', 'address': '', 'notes': '', 'contact_person': None, 'total_purchases': 5871.5, 'last_purchase_date': '2025/05/14 10:17:48', 'purchase_count': 1, 'total_debt': 1500.0}
تاريخ آخر عملية شراء للمورد احمد ربيع: 2025/05/14 10:17:48
تنسيق التاريخ: 2025/05/14 10:17:48
تاريخ آخر عملية شراء للمورد عمرو ابو رحمة: 2025/05/14 10:28:43
تنسيق التاريخ: 2025/05/14 10:28:43
تم إنشاء صفحة: الموردين
تم إضافة صفحة: الموردين إلى StackedWidget
جاري إنشاء زر وصفحة: المصروفات
تم إنشاء زر: المصروفات
جاري إنشاء صفحة: المصروفات
تم إنشاء صفحة: المصروفات
تم إضافة صفحة: المصروفات إلى StackedWidget
جاري إنشاء زر وصفحة: الإعدادات
تم إنشاء زر: الإعدادات
جاري إنشاء صفحة: الإعدادات
تم تحميل 2 مستخدم من قاعدة البيانات
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم تغيير السمة إلى: فاتح
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم إنشاء صفحة: الإعدادات
تم إضافة صفحة: الإعدادات إلى StackedWidget
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: جاري تحميل العملاء...
تم العثور على 0 عميل
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: عميل نقدي
تم تحديث صفحة المبيعات
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم قطع الاتصال بقاعدة البيانات
جاري تحديث معلومات المستخدم...
تم إنشاء النافذة الرئيسية بنجاح
تم إنشاء النافذة الرئيسية بنجاح
تم عرض النافذة الرئيسية
تم تغيير السمة إلى: فاتح
تم تحديث صفحة الإعدادات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تنسيق التاريخ: 2025/05/25 14:10:01
تم تحديث صفحة العملاء
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
Invoices after initial filtering (status, customer): 0
تم تحديث صفحة التقارير
تم قطع الاتصال بقاعدة البيانات
فلتر تاريخ البدء: 2025/05/31
فلتر تاريخ الانتهاء: 2025/05/31 (قبل 2025/06/01)
استعلام البحث: 
            SELECT i.id, i.reference_number, i.date, i.subtotal, i.tax, i.discount, 
                   i.total, i.paid_amount, i.remaining_amount, i.payment_method, i.status, 
                   i.notes, c.name as customer_name, c.id as customer_id
            FROM invoices i
            LEFT JOIN customers c ON i.customer_id = c.id
            WHERE 1=1
         AND (i.date >= ? OR i.date LIKE ?) AND i.date < ?c:\Users\<USER>\Desktop\11\main.pyw:86: DeprecationWarning: sipPyTypeDict() is deprecated, the extension module should use sipPyTypeDictRef() instead
  class MainWindow(QMainWindow):
استخدام الخط المحمل من الملف: Readex Pro
تم تغيير السمة إلى: فاتح
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم إنشاء الجداول بنجاح
تم حفظ التغييرات في قاعدة البيانات
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
تم حفظ التغييرات في قاعدة البيانات
لا توجد بيانات في جدول purchase_invoices، لا حاجة للترحيل.
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
المستخدم الرئيسي موجود بالفعل
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
عرض نافذة تسجيل الدخول...
تم حفظ التغييرات في قاعدة البيانات
نتيجة تسجيل الدخول: 1
بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-31 16:47:56', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-31 16:47:56', 'status': 'نشط'}
جاري إنشاء النافذة الرئيسية...
بدء إنشاء النافذة الرئيسية...
تم استدعاء منشئ الفئة الأب
تم تخزين بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-31 16:47:56', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-31 16:47:56', 'status': 'نشط'}
جاري تعيين الصلاحيات...
جاري إنشاء StackedWidget...
تم إنشاء StackedWidget
جاري إنشاء زر وصفحة: المبيعات
تم إنشاء زر: المبيعات
جاري إنشاء صفحة: المبيعات
خطأ في معالجة المنتجات المفضلة: 'SalesView' object has no attribute 'product_buttons'
تم إنشاء صفحة: المبيعات
تم إضافة صفحة: المبيعات إلى StackedWidget
جاري إنشاء زر وصفحة: المنتجات
تم إنشاء زر: المنتجات
جاري إنشاء صفحة: المنتجات
تم إنشاء صفحة: المنتجات
تم إضافة صفحة: المنتجات إلى StackedWidget
جاري إنشاء زر وصفحة: الفواتير
تم إنشاء زر: الفواتير
جاري إنشاء صفحة: الفواتير
فلتر تاريخ البدء: 2025/05/31
فلتر تاريخ الانتهاء: 2025/05/31 (قبل 2025/06/01)
استعلام البحث: 
            SELECT i.id, i.reference_number, i.date, i.subtotal, i.tax, i.discount, 
                   i.total, i.paid_amount, i.remaining_amount, i.payment_method, i.status, 
                   i.notes, c.name as customer_name, c.id as customer_id
            FROM invoices i
            LEFT JOIN customers c ON i.customer_id = c.id
            WHERE 1=1
         AND (i.date >= ? OR i.date LIKE ?) AND i.date < ?
معلمات البحث: ['2025/05/31', '2025/05/31%', '2025/06/01']
تم العثور على 0 فاتورة
تم إنشاء صفحة: الفواتير
تم إضافة صفحة: الفواتير إلى StackedWidget
جاري إنشاء زر وصفحة: التقارير
تم إنشاء زر: التقارير
جاري إنشاء صفحة: التقارير
Invoices after initial filtering (status, customer): 0
تم إنشاء صفحة: التقارير
تم إضافة صفحة: التقارير إلى StackedWidget
جاري إنشاء زر وصفحة: العملاء
تم إنشاء زر: العملاء
جاري إنشاء صفحة: العملاء
تنسيق التاريخ: 2025/05/25 14:10:01
تم إنشاء صفحة: العملاء
تم إضافة صفحة: العملاء إلى StackedWidget
جاري إنشاء زر وصفحة: الموردين
تم إنشاء زر: الموردين
جاري إنشاء صفحة: الموردين
تم استرجاع 2 مورد من قاعدة البيانات
أول مورد: {'id': 10, 'name': 'احمد ربيع', 'phone': '', 'email': '', 'address': '', 'notes': '', 'contact_person': None, 'total_purchases': 5871.5, 'last_purchase_date': '2025/05/14 10:17:48', 'purchase_count': 1, 'total_debt': 1500.0}
تاريخ آخر عملية شراء للمورد احمد ربيع: 2025/05/14 10:17:48
تنسيق التاريخ: 2025/05/14 10:17:48
تاريخ آخر عملية شراء للمورد عمرو ابو رحمة: 2025/05/14 10:28:43
تنسيق التاريخ: 2025/05/14 10:28:43
تم إنشاء صفحة: الموردين
تم إضافة صفحة: الموردين إلى StackedWidget
جاري إنشاء زر وصفحة: المصروفات
تم إنشاء زر: المصروفات
جاري إنشاء صفحة: المصروفات
تم إنشاء صفحة: المصروفات
تم إضافة صفحة: المصروفات إلى StackedWidget
جاري إنشاء زر وصفحة: الإعدادات
تم إنشاء زر: الإعدادات
جاري إنشاء صفحة: الإعدادات
تم تحميل 2 مستخدم من قاعدة البيانات
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم تغيير السمة إلى: داكن
تم تغيير السمة إلى: داكن
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم إنشاء صفحة: الإعدادات
تم إضافة صفحة: الإعدادات إلى StackedWidget
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: جاري تحميل العملاء...
تم العثور على 0 عميل
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: عميل نقدي
تم تحديث صفحة المبيعات
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم قطع الاتصال بقاعدة البيانات
جاري تحديث معلومات المستخدم...
تم إنشاء النافذة الرئيسية بنجاح
تم إنشاء النافذة الرئيسية بنجاح
تم عرض النافذة الرئيسية
تم تغيير السمة إلى: داكن
تم تحديث صفحة الإعدادات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم قطع الاتصال بقاعدة البيانات
تم تحديث محتوى تاب المستخدمين
تم تحديث محتوى تاب قاعدة البيانات والنسخ الاحتياطي
تم تحديث محتوى تاب المستخدمين
تم تحديث محتوى تاب قاعدة البيانات والنسخ الاحتياطي
تم تحديث محتوى تاب المستخدمين
تم تحديث محتوى تاب عام
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم استرجاع 2 مورد من قاعدة البيانات
أول مورد: {'id': 10, 'name': 'احمد ربيع', 'phone': '', 'email': '', 'address': '', 'notes': '', 'contact_person': None, 'total_purchases': 5871.5, 'last_purchase_date': '2025/05/14 10:17:48', 'purchase_count': 1, 'total_debt': 1500.0}
تاريخ آخر عملية شراء للمورد احمد ربيع: 2025/05/14 10:17:48
تنسيق التاريخ: 2025/05/14 10:17:48
تاريخ آخر عملية شراء للمورد عمرو ابو رحمة: 2025/05/14 10:28:43
تنسيق التاريخ: 2025/05/14 10:28:43
تم تحديث صفحة الموردين
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تنسيق التاريخ: 2025/05/25 14:10:01
تم تحديث صفحة العملاء
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
Invoices after initial filtering (status, customer): 0
تم تحديث صفحة التقارير
تم قطع الاتصال بقاعدة البيانات
فلتر تاريخ البدء: 2025/05/31
فلتر تاريخ الانتهاء: 2025/05/31 (قبل 2025/06/01)
استعلام البحث: 
            SELECT i.id, i.reference_number, i.date, i.subtotal, i.tax, i.discount, 
                   i.total, i.paid_amount, i.remaining_amount, i.payment_method, i.status, 
                   i.notes, c.name as customer_name, c.id as customer_id
            FROM invoices i
            LEFT JOIN customers c ON i.customer_id = c.id
            WHERE 1=1
         AND (i.date >= ? OR i.date LIKE ?) AND i.date < ?
معلمات البحث: ['2025/05/31', '2025/05/31%', '2025/06/01']
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم العثور على 0 فاتورة
تم تحديث صفحة الفواتير
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم تحديث صفحة المنتجات
تم قطع الاتصال بقاعدة البيانات
تم اختيار العميل: 
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم العثور على 1 عميل
تم اختيار العميل: جاري تحميل العملاء...
تم العثور على 0 عميل
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: عميل نقدي
تم تحديث صفحة المبيعات
تم قطع الاتصال بقاعدة البيانات
تم تغيير السمة إلى: داكن
تم تحديث صفحة الإعدادات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم قطع الاتصال بقاعدة البيانات
تم تغيير السمة إلى: فاتح
تم تغيير السمة إلى: فاتح
تم تغيير السمة إلى: فاتح
c:\Users\<USER>\Desktop\11\main.pyw:86: DeprecationWarning: sipPyTypeDict() is deprecated, the extension module should use sipPyTypeDictRef() instead
  class MainWindow(QMainWindow):
استخدام الخط المحمل من الملف: Readex Pro
تم تغيير السمة إلى: فاتح
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم إنشاء الجداول بنجاح
تم حفظ التغييرات في قاعدة البيانات
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
تم حفظ التغييرات في قاعدة البيانات
لا توجد بيانات في جدول purchase_invoices، لا حاجة للترحيل.
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
المستخدم الرئيسي موجود بالفعل
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
عرض نافذة تسجيل الدخول...
تم حفظ التغييرات في قاعدة البيانات
نتيجة تسجيل الدخول: 1
بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-31 16:48:28', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-31 16:48:28', 'status': 'نشط'}
جاري إنشاء النافذة الرئيسية...
بدء إنشاء النافذة الرئيسية...
تم استدعاء منشئ الفئة الأب
تم تخزين بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-31 16:48:28', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-31 16:48:28', 'status': 'نشط'}
جاري تعيين الصلاحيات...
جاري إنشاء StackedWidget...
تم إنشاء StackedWidget
جاري إنشاء زر وصفحة: المبيعات
تم إنشاء زر: المبيعات
جاري إنشاء صفحة: المبيعات
خطأ في معالجة المنتجات المفضلة: 'SalesView' object has no attribute 'product_buttons'
تم إنشاء صفحة: المبيعات
تم إضافة صفحة: المبيعات إلى StackedWidget
جاري إنشاء زر وصفحة: المنتجات
تم إنشاء زر: المنتجات
جاري إنشاء صفحة: المنتجات
تم إنشاء صفحة: المنتجات
تم إضافة صفحة: المنتجات إلى StackedWidget
جاري إنشاء زر وصفحة: الفواتير
تم إنشاء زر: الفواتير
جاري إنشاء صفحة: الفواتير
فلتر تاريخ البدء: 2025/05/31
فلتر تاريخ الانتهاء: 2025/05/31 (قبل 2025/06/01)
استعلام البحث: 
            SELECT i.id, i.reference_number, i.date, i.subtotal, i.tax, i.discount, 
                   i.total, i.paid_amount, i.remaining_amount, i.payment_method, i.status, 
                   i.notes, c.name as customer_name, c.id as customer_id
            FROM invoices i
            LEFT JOIN customers c ON i.customer_id = c.id
            WHERE 1=1
         AND (i.date >= ? OR i.date LIKE ?) AND i.date < ?
معلمات البحث: ['2025/05/31', '2025/05/31%', '2025/06/01']
تم العثور على 0 فاتورة
تم إنشاء صفحة: الفواتير
تم إضافة صفحة: الفواتير إلى StackedWidget
جاري إنشاء زر وصفحة: التقارير
تم إنشاء زر: التقارير
جاري إنشاء صفحة: التقارير
Invoices after initial filtering (status, customer): 0
تم إنشاء صفحة: التقارير
تم إضافة صفحة: التقارير إلى StackedWidget
جاري إنشاء زر وصفحة: العملاء
تم إنشاء زر: العملاء
جاري إنشاء صفحة: العملاء
تنسيق التاريخ: 2025/05/25 14:10:01
تم إنشاء صفحة: العملاء
تم إضافة صفحة: العملاء إلى StackedWidget
جاري إنشاء زر وصفحة: الموردين
تم إنشاء زر: الموردين
جاري إنشاء صفحة: الموردين
تم استرجاع 2 مورد من قاعدة البيانات
أول مورد: {'id': 10, 'name': 'احمد ربيع', 'phone': '', 'email': '', 'address': '', 'notes': '', 'contact_person': None, 'total_purchases': 5871.5, 'last_purchase_date': '2025/05/14 10:17:48', 'purchase_count': 1, 'total_debt': 1500.0}
تاريخ آخر عملية شراء للمورد احمد ربيع: 2025/05/14 10:17:48
تنسيق التاريخ: 2025/05/14 10:17:48
تاريخ آخر عملية شراء للمورد عمرو ابو رحمة: 2025/05/14 10:28:43
تنسيق التاريخ: 2025/05/14 10:28:43
تم إنشاء صفحة: الموردين
تم إضافة صفحة: الموردين إلى StackedWidget
جاري إنشاء زر وصفحة: المصروفات
تم إنشاء زر: المصروفات
جاري إنشاء صفحة: المصروفات
تم إنشاء صفحة: المصروفات
تم إضافة صفحة: المصروفات إلى StackedWidget
جاري إنشاء زر وصفحة: الإعدادات
تم إنشاء زر: الإعدادات
جاري إنشاء صفحة: الإعدادات
تم تحميل 2 مستخدم من قاعدة البيانات
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم تغيير السمة إلى: فاتح
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم إنشاء صفحة: الإعدادات
تم إضافة صفحة: الإعدادات إلى StackedWidget
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: جاري تحميل العملاء...
تم العثور على 0 عميل
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: عميل نقدي
تم تحديث صفحة المبيعات
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم قطع الاتصال بقاعدة البيانات
جاري تحديث معلومات المستخدم...
تم إنشاء النافذة الرئيسية بنجاح
تم إنشاء النافذة الرئيسية بنجاح
تم عرض النافذة الرئيسية
c:\Users\<USER>\Desktop\11\main.pyw:86: DeprecationWarning: sipPyTypeDict() is deprecated, the extension module should use sipPyTypeDictRef() instead
  class MainWindow(QMainWindow):
استخدام الخط المحمل من الملف: Readex Pro
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم إنشاء الجداول بنجاح
تم حفظ التغييرات في قاعدة البيانات
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
تم حفظ التغييرات في قاعدة البيانات
لا توجد بيانات في جدول purchase_invoices، لا حاجة للترحيل.
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
المستخدم الرئيسي موجود بالفعل
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
عرض نافذة تسجيل الدخول...
تم حفظ التغييرات في قاعدة البيانات
نتيجة تسجيل الدخول: 1
بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-31 16:49:03', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-31 16:49:03', 'status': 'نشط'}
جاري إنشاء النافذة الرئيسية...
بدء إنشاء النافذة الرئيسية...
تم استدعاء منشئ الفئة الأب
تم تخزين بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-31 16:49:03', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-31 16:49:03', 'status': 'نشط'}
جاري تعيين الصلاحيات...
جاري إنشاء StackedWidget...
تم إنشاء StackedWidget
جاري إنشاء زر وصفحة: المبيعات
تم إنشاء زر: المبيعات
جاري إنشاء صفحة: المبيعات
خطأ في معالجة المنتجات المفضلة: 'SalesView' object has no attribute 'product_buttons'
تم إنشاء صفحة: المبيعات
تم إضافة صفحة: المبيعات إلى StackedWidget
جاري إنشاء زر وصفحة: المنتجات
تم إنشاء زر: المنتجات
جاري إنشاء صفحة: المنتجات
تم إنشاء صفحة: المنتجات
تم إضافة صفحة: المنتجات إلى StackedWidget
جاري إنشاء زر وصفحة: الفواتير
تم إنشاء زر: الفواتير
جاري إنشاء صفحة: الفواتير
فلتر تاريخ البدء: 2025/05/31
فلتر تاريخ الانتهاء: 2025/05/31 (قبل 2025/06/01)
استعلام البحث: 
            SELECT i.id, i.reference_number, i.date, i.subtotal, i.tax, i.discount, 
                   i.total, i.paid_amount, i.remaining_amount, i.payment_method, i.status, 
                   i.notes, c.name as customer_name, c.id as customer_id
            FROM invoices i
            LEFT JOIN customers c ON i.customer_id = c.id
            WHERE 1=1
         AND (i.date >= ? OR i.date LIKE ?) AND i.date < ?
معلمات البحث: ['2025/05/31', '2025/05/31%', '2025/06/01']
تم العثور على 0 فاتورة
تم إنشاء صفحة: الفواتير
تم إضافة صفحة: الفواتير إلى StackedWidget
جاري إنشاء زر وصفحة: التقارير
تم إنشاء زر: التقارير
جاري إنشاء صفحة: التقارير
Invoices after initial filtering (status, customer): 0
تم إنشاء صفحة: التقارير
تم إضافة صفحة: التقارير إلى StackedWidget
جاري إنشاء زر وصفحة: العملاء
تم إنشاء زر: العملاء
جاري إنشاء صفحة: العملاء
تنسيق التاريخ: 2025/05/25 14:10:01
تم إنشاء صفحة: العملاء
تم إضافة صفحة: العملاء إلى StackedWidget
جاري إنشاء زر وصفحة: الموردين
تم إنشاء زر: الموردين
جاري إنشاء صفحة: الموردين
تم استرجاع 2 مورد من قاعدة البيانات
أول مورد: {'id': 10, 'name': 'احمد ربيع', 'phone': '', 'email': '', 'address': '', 'notes': '', 'contact_person': None, 'total_purchases': 5871.5, 'last_purchase_date': '2025/05/14 10:17:48', 'purchase_count': 1, 'total_debt': 1500.0}
تاريخ آخر عملية شراء للمورد احمد ربيع: 2025/05/14 10:17:48
تنسيق التاريخ: 2025/05/14 10:17:48
تاريخ آخر عملية شراء للمورد عمرو ابو رحمة: 2025/05/14 10:28:43
تنسيق التاريخ: 2025/05/14 10:28:43
تم إنشاء صفحة: الموردين
تم إضافة صفحة: الموردين إلى StackedWidget
جاري إنشاء زر وصفحة: المصروفات
تم إنشاء زر: المصروفات
جاري إنشاء صفحة: المصروفات
تم إنشاء صفحة: المصروفات
تم إضافة صفحة: المصروفات إلى StackedWidget
جاري إنشاء زر وصفحة: الإعدادات
تم إنشاء زر: الإعدادات
جاري إنشاء صفحة: الإعدادات
تم تحميل 2 مستخدم من قاعدة البيانات
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم إنشاء صفحة: الإعدادات
تم إضافة صفحة: الإعدادات إلى StackedWidget
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: جاري تحميل العملاء...
تم العثور على 0 عميل
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: عميل نقدي
تم تحديث صفحة المبيعات
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم قطع الاتصال بقاعدة البيانات
جاري تحديث معلومات المستخدم...
تم إنشاء النافذة الرئيسية بنجاح
تم إنشاء النافذة الرئيسية بنجاح
تم عرض النافذة الرئيسية
C:\Users\<USER>\Desktop\11\main.pyw:86: DeprecationWarning: sipPyTypeDict() is deprecated, the extension module should use sipPyTypeDictRef() instead
  class MainWindow(QMainWindow):
استخدام الخط المحمل من الملف: Readex Pro
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم إنشاء الجداول بنجاح
تم حفظ التغييرات في قاعدة البيانات
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
تم حفظ التغييرات في قاعدة البيانات
لا توجد بيانات في جدول purchase_invoices، لا حاجة للترحيل.
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
المستخدم الرئيسي موجود بالفعل
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
عرض نافذة تسجيل الدخول...
تم حفظ التغييرات في قاعدة البيانات
نتيجة تسجيل الدخول: 1
بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-31 16:49:21', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-31 16:49:21', 'status': 'نشط'}
جاري إنشاء النافذة الرئيسية...
بدء إنشاء النافذة الرئيسية...
تم استدعاء منشئ الفئة الأب
تم تخزين بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-31 16:49:21', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-31 16:49:21', 'status': 'نشط'}
جاري تعيين الصلاحيات...
جاري إنشاء StackedWidget...
تم إنشاء StackedWidget
جاري إنشاء زر وصفحة: المبيعات
تم إنشاء زر: المبيعات
جاري إنشاء صفحة: المبيعات
خطأ في معالجة المنتجات المفضلة: 'SalesView' object has no attribute 'product_buttons'
تم إنشاء صفحة: المبيعات
تم إضافة صفحة: المبيعات إلى StackedWidget
جاري إنشاء زر وصفحة: المنتجات
تم إنشاء زر: المنتجات
جاري إنشاء صفحة: المنتجات
تم إنشاء صفحة: المنتجات
تم إضافة صفحة: المنتجات إلى StackedWidget
جاري إنشاء زر وصفحة: الفواتير
تم إنشاء زر: الفواتير
جاري إنشاء صفحة: الفواتير
فلتر تاريخ البدء: 2025/05/31
فلتر تاريخ الانتهاء: 2025/05/31 (قبل 2025/06/01)
استعلام البحث: 
            SELECT i.id, i.reference_number, i.date, i.subtotal, i.tax, i.discount, 
                   i.total, i.paid_amount, i.remaining_amount, i.payment_method, i.status, 
                   i.notes, c.name as customer_name, c.id as customer_id
            FROM invoices i
            LEFT JOIN customers c ON i.customer_id = c.id
            WHERE 1=1
         AND (i.date >= ? OR i.date LIKE ?) AND i.date < ?
معلمات البحث: ['2025/05/31', '2025/05/31%', '2025/06/01']
تم العثور على 0 فاتورة
تم إنشاء صفحة: الفواتير
تم إضافة صفحة: الفواتير إلى StackedWidget
جاري إنشاء زر وصفحة: التقارير
تم إنشاء زر: التقارير
جاري إنشاء صفحة: التقارير
Invoices after initial filtering (status, customer): 0
تم إنشاء صفحة: التقارير
تم إضافة صفحة: التقارير إلى StackedWidget
جاري إنشاء زر وصفحة: العملاء
تم إنشاء زر: العملاء
جاري إنشاء صفحة: العملاء
تنسيق التاريخ: 2025/05/25 14:10:01
تم إنشاء صفحة: العملاء
تم إضافة صفحة: العملاء إلى StackedWidget
جاري إنشاء زر وصفحة: الموردين
تم إنشاء زر: الموردين
جاري إنشاء صفحة: الموردين
تم استرجاع 2 مورد من قاعدة البيانات
أول مورد: {'id': 10, 'name': 'احمد ربيع', 'phone': '', 'email': '', 'address': '', 'notes': '', 'contact_person': None, 'total_purchases': 5871.5, 'last_purchase_date': '2025/05/14 10:17:48', 'purchase_count': 1, 'total_debt': 1500.0}
تاريخ آخر عملية شراء للمورد احمد ربيع: 2025/05/14 10:17:48
تنسيق التاريخ: 2025/05/14 10:17:48
تاريخ آخر عملية شراء للمورد عمرو ابو رحمة: 2025/05/14 10:28:43
تنسيق التاريخ: 2025/05/14 10:28:43
تم إنشاء صفحة: الموردين
تم إضافة صفحة: الموردين إلى StackedWidget
جاري إنشاء زر وصفحة: المصروفات
تم إنشاء زر: المصروفات
جاري إنشاء صفحة: المصروفات
تم إنشاء صفحة: المصروفات
تم إضافة صفحة: المصروفات إلى StackedWidget
جاري إنشاء زر وصفحة: الإعدادات
تم إنشاء زر: الإعدادات
جاري إنشاء صفحة: الإعدادات
تم تحميل 2 مستخدم من قاعدة البيانات
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم إنشاء صفحة: الإعدادات
تم إضافة صفحة: الإعدادات إلى StackedWidget
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: جاري تحميل العملاء...
تم العثور على 0 عميل
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: عميل نقدي
تم تحديث صفحة المبيعات
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم قطع الاتصال بقاعدة البيانات
جاري تحديث معلومات المستخدم...
تم إنشاء النافذة الرئيسية بنجاح
تم إنشاء النافذة الرئيسية بنجاح
تم عرض النافذة الرئيسية
تم تحديث صفحة الإعدادات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم قطع الاتصال بقاعدة البيانات
c:\Users\<USER>\Desktop\11\main.pyw:86: DeprecationWarning: sipPyTypeDict() is deprecated, the extension module should use sipPyTypeDictRef() instead
  class MainWindow(QMainWindow):
استخدام الخط المحمل من الملف: Readex Pro
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم إنشاء الجداول بنجاح
تم حفظ التغييرات في قاعدة البيانات
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
تم حفظ التغييرات في قاعدة البيانات
لا توجد بيانات في جدول purchase_invoices، لا حاجة للترحيل.
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
المستخدم الرئيسي موجود بالفعل
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
عرض نافذة تسجيل الدخول...
تم حفظ التغييرات في قاعدة البيانات
نتيجة تسجيل الدخول: 1
بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-31 16:50:57', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-31 16:50:57', 'status': 'نشط'}
جاري إنشاء النافذة الرئيسية...
بدء إنشاء النافذة الرئيسية...
تم استدعاء منشئ الفئة الأب
تم تخزين بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-31 16:50:57', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-31 16:50:57', 'status': 'نشط'}
جاري تعيين الصلاحيات...
جاري إنشاء StackedWidget...
تم إنشاء StackedWidget
جاري إنشاء زر وصفحة: المبيعات
تم إنشاء زر: المبيعات
جاري إنشاء صفحة: المبيعات
خطأ في معالجة المنتجات المفضلة: 'SalesView' object has no attribute 'product_buttons'
تم إنشاء صفحة: المبيعات
تم إضافة صفحة: المبيعات إلى StackedWidget
جاري إنشاء زر وصفحة: المنتجات
تم إنشاء زر: المنتجات
جاري إنشاء صفحة: المنتجات
تم إنشاء صفحة: المنتجات
تم إضافة صفحة: المنتجات إلى StackedWidget
جاري إنشاء زر وصفحة: الفواتير
تم إنشاء زر: الفواتير
جاري إنشاء صفحة: الفواتير
فلتر تاريخ البدء: 2025/05/31
فلتر تاريخ الانتهاء: 2025/05/31 (قبل 2025/06/01)
استعلام البحث: 
            SELECT i.id, i.reference_number, i.date, i.subtotal, i.tax, i.discount, 
                   i.total, i.paid_amount, i.remaining_amount, i.payment_method, i.status, 
                   i.notes, c.name as customer_name, c.id as customer_id
            FROM invoices i
            LEFT JOIN customers c ON i.customer_id = c.id
            WHERE 1=1
         AND (i.date >= ? OR i.date LIKE ?) AND i.date < ?
معلمات البحث: ['2025/05/31', '2025/05/31%', '2025/06/01']
تم العثور على 0 فاتورة
تم إنشاء صفحة: الفواتير
تم إضافة صفحة: الفواتير إلى StackedWidget
جاري إنشاء زر وصفحة: التقارير
تم إنشاء زر: التقارير
جاري إنشاء صفحة: التقارير
Invoices after initial filtering (status, customer): 0
تم إنشاء صفحة: التقارير
تم إضافة صفحة: التقارير إلى StackedWidget
جاري إنشاء زر وصفحة: العملاء
تم إنشاء زر: العملاء
جاري إنشاء صفحة: العملاء
تنسيق التاريخ: 2025/05/25 14:10:01
تم إنشاء صفحة: العملاء
تم إضافة صفحة: العملاء إلى StackedWidget
جاري إنشاء زر وصفحة: الموردين
تم إنشاء زر: الموردين
جاري إنشاء صفحة: الموردين
تم استرجاع 2 مورد من قاعدة البيانات
أول مورد: {'id': 10, 'name': 'احمد ربيع', 'phone': '', 'email': '', 'address': '', 'notes': '', 'contact_person': None, 'total_purchases': 5871.5, 'last_purchase_date': '2025/05/14 10:17:48', 'purchase_count': 1, 'total_debt': 1500.0}
تاريخ آخر عملية شراء للمورد احمد ربيع: 2025/05/14 10:17:48
تنسيق التاريخ: 2025/05/14 10:17:48
تاريخ آخر عملية شراء للمورد عمرو ابو رحمة: 2025/05/14 10:28:43
تنسيق التاريخ: 2025/05/14 10:28:43
تم إنشاء صفحة: الموردين
تم إضافة صفحة: الموردين إلى StackedWidget
جاري إنشاء زر وصفحة: المصروفات
تم إنشاء زر: المصروفات
جاري إنشاء صفحة: المصروفات
تم إنشاء صفحة: المصروفات
تم إضافة صفحة: المصروفات إلى StackedWidget
جاري إنشاء زر وصفحة: الإعدادات
تم إنشاء زر: الإعدادات
جاري إنشاء صفحة: الإعدادات
تم تحميل 2 مستخدم من قاعدة البيانات
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم إنشاء صفحة: الإعدادات
تم إضافة صفحة: الإعدادات إلى StackedWidget
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: جاري تحميل العملاء...
تم العثور على 0 عميل
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: عميل نقدي
تم تحديث صفحة المبيعات
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم قطع الاتصال بقاعدة البيانات
جاري تحديث معلومات المستخدم...
تم إنشاء النافذة الرئيسية بنجاح
تم إنشاء النافذة الرئيسية بنجاح
تم عرض النافذة الرئيسية
تم تحديث صفحة الإعدادات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم قطع الاتصال بقاعدة البيانات
تم تحديث محتوى تاب المستخدمين
تم تحديث محتوى تاب قاعدة البيانات والنسخ الاحتياطي
تم تحديث محتوى تاب عام
تم اختيار العميل: 
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم العثور على 1 عميل
تم اختيار العميل: جاري تحميل العملاء...
تم العثور على 0 عميل
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: عميل نقدي
تم تحديث صفحة المبيعات
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم تحديث صفحة المنتجات
تم قطع الاتصال بقاعدة البيانات
تم اختيار العميل: 
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم العثور على 1 عميل
تم اختيار العميل: جاري تحميل العملاء...
تم العثور على 0 عميل
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: عميل نقدي
تم تحديث صفحة المبيعات
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم تحديث صفحة المنتجات
تم قطع الاتصال بقاعدة البيانات
فلتر تاريخ البدء: 2025/05/31
فلتر تاريخ الانتهاء: 2025/05/31 (قبل 2025/06/01)
استعلام البحث: 
            SELECT i.id, i.reference_number, i.date, i.subtotal, i.tax, i.discount, 
                   i.total, i.paid_amount, i.remaining_amount, i.payment_method, i.status, 
                   i.notes, c.name as customer_name, c.id as customer_id
            FROM invoices i
            LEFT JOIN customers c ON i.customer_id = c.id
            WHERE 1=1
         AND (i.date >= ? OR i.date LIKE ?) AND i.date < ?
معلمات البحث: ['2025/05/31', '2025/05/31%', '2025/06/01']
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم العثور على 0 فاتورة
تم تحديث صفحة الفواتير
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
Invoices after initial filtering (status, customer): 0
تم تحديث صفحة التقارير
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
Invoices after initial filtering (status, customer): 0
c:\Users\<USER>\Desktop\11\main.pyw:86: DeprecationWarning: sipPyTypeDict() is deprecated, the extension module should use sipPyTypeDictRef() instead
  class MainWindow(QMainWindow):
استخدام الخط المحمل من الملف: Readex Pro
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم إنشاء الجداول بنجاح
تم حفظ التغييرات في قاعدة البيانات
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
تم حفظ التغييرات في قاعدة البيانات
لا توجد بيانات في جدول purchase_invoices، لا حاجة للترحيل.
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'payment_date', 'amount', 'payment_method', 'notes', 'user_id', 'created_at']
تم حفظ التغييرات في قاعدة البيانات
المستخدم الرئيسي موجود بالفعل
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
عرض نافذة تسجيل الدخول...
تم حفظ التغييرات في قاعدة البيانات
نتيجة تسجيل الدخول: 1
بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-31 16:59:18', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-31 16:59:18', 'status': 'نشط'}
جاري إنشاء النافذة الرئيسية...
بدء إنشاء النافذة الرئيسية...
تم استدعاء منشئ الفئة الأب
تم تخزين بيانات المستخدم: {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'password': '5994471abb01112afcc18159f6cc74b4f511b99806da59b3caf5a9c173cacfc5', 'role': 'مدير', 'is_active': True, 'last_login': '2025-05-31 16:59:18', 'created_at': '2025-05-13 19:30:01', 'updated_at': '2025-05-31 16:59:18', 'status': 'نشط'}
جاري تعيين الصلاحيات...
جاري إنشاء StackedWidget...
تم إنشاء StackedWidget
جاري إنشاء زر وصفحة: المبيعات
تم إنشاء زر: المبيعات
جاري إنشاء صفحة: المبيعات
خطأ في معالجة المنتجات المفضلة: 'SalesView' object has no attribute 'product_buttons'
تم إنشاء صفحة: المبيعات
تم إضافة صفحة: المبيعات إلى StackedWidget
جاري إنشاء زر وصفحة: المنتجات
تم إنشاء زر: المنتجات
جاري إنشاء صفحة: المنتجات
تم إنشاء صفحة: المنتجات
تم إضافة صفحة: المنتجات إلى StackedWidget
جاري إنشاء زر وصفحة: الفواتير
تم إنشاء زر: الفواتير
جاري إنشاء صفحة: الفواتير
فلتر تاريخ البدء: 2025/05/31
فلتر تاريخ الانتهاء: 2025/05/31 (قبل 2025/06/01)
استعلام البحث: 
            SELECT i.id, i.reference_number, i.date, i.subtotal, i.tax, i.discount, 
                   i.total, i.paid_amount, i.remaining_amount, i.payment_method, i.status, 
                   i.notes, c.name as customer_name, c.id as customer_id
            FROM invoices i
            LEFT JOIN customers c ON i.customer_id = c.id
            WHERE 1=1
         AND (i.date >= ? OR i.date LIKE ?) AND i.date < ?
معلمات البحث: ['2025/05/31', '2025/05/31%', '2025/06/01']
تم العثور على 0 فاتورة
تم إنشاء صفحة: الفواتير
تم إضافة صفحة: الفواتير إلى StackedWidget
جاري إنشاء زر وصفحة: التقارير
تم إنشاء زر: التقارير
جاري إنشاء صفحة: التقارير
Invoices after initial filtering (status, customer): 0
تم إنشاء صفحة: التقارير
تم إضافة صفحة: التقارير إلى StackedWidget
جاري إنشاء زر وصفحة: العملاء
تم إنشاء زر: العملاء
جاري إنشاء صفحة: العملاء
تنسيق التاريخ: 2025/05/25 14:10:01
تم إنشاء صفحة: العملاء
تم إضافة صفحة: العملاء إلى StackedWidget
جاري إنشاء زر وصفحة: الموردين
تم إنشاء زر: الموردين
جاري إنشاء صفحة: الموردين
تم استرجاع 2 مورد من قاعدة البيانات
أول مورد: {'id': 10, 'name': 'احمد ربيع', 'phone': '', 'email': '', 'address': '', 'notes': '', 'contact_person': None, 'total_purchases': 5871.5, 'last_purchase_date': '2025/05/14 10:17:48', 'purchase_count': 1, 'total_debt': 1500.0}
تاريخ آخر عملية شراء للمورد احمد ربيع: 2025/05/14 10:17:48
تنسيق التاريخ: 2025/05/14 10:17:48
تاريخ آخر عملية شراء للمورد عمرو ابو رحمة: 2025/05/14 10:28:43
تنسيق التاريخ: 2025/05/14 10:28:43
تم إنشاء صفحة: الموردين
تم إضافة صفحة: الموردين إلى StackedWidget
جاري إنشاء زر وصفحة: المصروفات
تم إنشاء زر: المصروفات
جاري إنشاء صفحة: المصروفات
تم إنشاء صفحة: المصروفات
تم إضافة صفحة: المصروفات إلى StackedWidget
جاري إنشاء زر وصفحة: الإعدادات
تم إنشاء زر: الإعدادات
جاري إنشاء صفحة: الإعدادات
تم تحميل 2 مستخدم من قاعدة البيانات
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم إنشاء صفحة: الإعدادات
تم إضافة صفحة: الإعدادات إلى StackedWidget
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: جاري تحميل العملاء...
تم العثور على 0 عميل
تم اختيار العميل: 
تم العثور على 1 عميل
تم اختيار العميل: عميل نقدي
تم تحديث صفحة المبيعات
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم قطع الاتصال بقاعدة البيانات
جاري تحديث معلومات المستخدم...
تم إنشاء النافذة الرئيسية بنجاح
تم إنشاء النافذة الرئيسية بنجاح
تم عرض النافذة الرئيسية
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم تحديث صفحة المنتجات
تم قطع الاتصال بقاعدة البيانات
فلتر تاريخ البدء: 2025/05/31
فلتر تاريخ الانتهاء: 2025/05/31 (قبل 2025/06/01)
استعلام البحث: 
            SELECT i.id, i.reference_number, i.date, i.subtotal, i.tax, i.discount, 
                   i.total, i.paid_amount, i.remaining_amount, i.payment_method, i.status, 
                   i.notes, c.name as customer_name, c.id as customer_id
            FROM invoices i
            LEFT JOIN customers c ON i.customer_id = c.id
            WHERE 1=1
         AND (i.date >= ? OR i.date LIKE ?) AND i.date < ?
معلمات البحث: ['2025/05/31', '2025/05/31%', '2025/06/01']
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم العثور على 0 فاتورة
تم تحديث صفحة الفواتير
تم قطع الاتصال بقاعدة البيانات